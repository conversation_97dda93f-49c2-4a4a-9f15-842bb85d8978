#include <catch2/catch_test_macros.hpp>
#include "core/MarketData.h"
#include "core/Types.h"
#include <vector>
#include <algorithm>

using namespace DataHub::Core;

TEST_CASE("Data Aggregation - Bar aggregation", "[aggregation]") {
    SECTION("Minute bars to 5-minute bars") {
        std::vector<BarData> minute_bars;
        
        // Create 5 minute bars
        for (int i = 0; i < 5; ++i) {
            BarData bar("000001.SZ", now(), BarSize::Minute1, BarType::Candle);
            bar.open = 10.0 + i * 0.1;
            bar.high = 10.2 + i * 0.1;
            bar.low = 9.9 + i * 0.1;
            bar.close = 10.1 + i * 0.1;
            bar.volume = 1000 + i * 100;
            bar.amount = (10.05 + i * 0.1) * (1000 + i * 100);
            minute_bars.push_back(bar);
        }
        
        // Aggregate to 5-minute bar
        BarData aggregated("000001.SZ", minute_bars[0].timestamp, BarSize::Minute5, BarType::Candle);
        aggregated.open = minute_bars[0].open;
        aggregated.close = minute_bars.back().close;
        
        // Find high and low
        aggregated.high = std::max_element(minute_bars.begin(), minute_bars.end(),
            [](const BarData& a, const BarData& b) { return a.high < b.high; })->high;
        aggregated.low = std::min_element(minute_bars.begin(), minute_bars.end(),
            [](const BarData& a, const BarData& b) { return a.low < b.low; })->low;
        
        // Sum volume and amount
        aggregated.volume = 0;
        aggregated.amount = 0.0;
        for (const auto& bar : minute_bars) {
            aggregated.volume += bar.volume;
            aggregated.amount += bar.amount;
        }
        
        REQUIRE(aggregated.open == 10.0);
        REQUIRE(aggregated.close == 10.5);
        REQUIRE(aggregated.high == 10.6);
        REQUIRE(aggregated.low == 9.9);
        REQUIRE(aggregated.volume == 6000);  // 1000+1100+1200+1300+1400 = 6000
        REQUIRE(aggregated.is_valid());
    }
    
    SECTION("Volume weighted average price") {
        std::vector<BarData> bars;
        
        // Create test bars with different volumes
        for (int i = 0; i < 3; ++i) {
            BarData bar("000001.SZ", now(), BarSize::Minute1, BarType::Candle);
            bar.open = 10.0;
            bar.high = 10.2;
            bar.low = 9.8;
            bar.close = 10.0 + i * 0.1;  // 10.0, 10.1, 10.2
            bar.volume = (i + 1) * 1000;  // 1000, 2000, 3000
            bar.amount = bar.close * bar.volume;
            bars.push_back(bar);
        }
        
        // Calculate VWAP
        double total_amount = 0.0;
        Volume total_volume = 0;
        for (const auto& bar : bars) {
            total_amount += bar.amount;
            total_volume += bar.volume;
        }
        
        double vwap = total_amount / static_cast<double>(total_volume);
        
        // Expected: (10.0*1000 + 10.1*2000 + 10.2*3000) / (1000+2000+3000)
        // = (10000 + 20200 + 30600) / 6000 = 60800 / 6000 = 10.133...
        REQUIRE(is_equal(vwap, 10.133333, 1e-5));
    }
}

TEST_CASE("Data Aggregation - Quote aggregation", "[aggregation]") {
    SECTION("Best bid/ask from multiple quotes") {
        std::vector<QuoteData> quotes;
        
        // Create test quotes with different bid/ask prices
        for (int i = 0; i < 3; ++i) {
            QuoteData quote;
            quote.symbol = "000001.SZ";
            quote.timestamp = now();
            quote.bid_prices[0] = 10.0 + i * 0.01;  // 10.00, 10.01, 10.02
            quote.ask_prices[0] = 10.05 + i * 0.01; // 10.05, 10.06, 10.07
            quote.bid_volumes[0] = 1000 + i * 100;
            quote.ask_volumes[0] = 1500 + i * 100;
            quotes.push_back(quote);
        }
        
        // Find best bid (highest) and best ask (lowest)
        auto best_bid_it = std::max_element(quotes.begin(), quotes.end(),
            [](const QuoteData& a, const QuoteData& b) { 
                return a.bid_prices[0] < b.bid_prices[0]; 
            });
        
        auto best_ask_it = std::min_element(quotes.begin(), quotes.end(),
            [](const QuoteData& a, const QuoteData& b) { 
                return a.ask_prices[0] < b.ask_prices[0]; 
            });
        
        REQUIRE(best_bid_it->bid_prices[0] == 10.02);
        REQUIRE(best_ask_it->ask_prices[0] == 10.05);
        
        // Calculate spread
        double spread = best_ask_it->ask_prices[0] - best_bid_it->bid_prices[0];
        REQUIRE(is_equal(spread, 0.03, 1e-6));
    }
}

TEST_CASE("Data Aggregation - Statistical calculations", "[aggregation]") {
    SECTION("Price statistics") {
        std::vector<double> prices = {10.0, 10.5, 9.8, 10.2, 10.1, 9.9, 10.3};
        
        // Calculate mean
        double sum = std::accumulate(prices.begin(), prices.end(), 0.0);
        double mean = sum / prices.size();
        
        // Calculate variance
        double variance = 0.0;
        for (double price : prices) {
            variance += (price - mean) * (price - mean);
        }
        variance /= prices.size();
        
        // Calculate standard deviation
        double std_dev = std::sqrt(variance);
        
        REQUIRE(is_equal(mean, 10.114286, 1e-5));
        REQUIRE(std_dev > 0.0);
        
        // Find min and max
        auto [min_it, max_it] = std::minmax_element(prices.begin(), prices.end());
        REQUIRE(*min_it == 9.8);
        REQUIRE(*max_it == 10.5);
        
        // Calculate range
        double range = *max_it - *min_it;
        REQUIRE(is_equal(range, 0.7, 1e-6));
    }
    
    SECTION("Volume statistics") {
        std::vector<Volume> volumes = {1000, 1500, 800, 1200, 2000, 900, 1100};
        
        // Calculate total volume
        Volume total = std::accumulate(volumes.begin(), volumes.end(), Volume{0});
        REQUIRE(total == 8500);
        
        // Calculate average volume
        double avg_volume = static_cast<double>(total) / volumes.size();
        REQUIRE(is_equal(avg_volume, 1214.286, 1e-2));
        
        // Find median volume
        std::vector<Volume> sorted_volumes = volumes;
        std::sort(sorted_volumes.begin(), sorted_volumes.end());
        Volume median = sorted_volumes[sorted_volumes.size() / 2];
        REQUIRE(median == 1100);
    }
}

TEST_CASE("Data Aggregation - Time-based grouping", "[aggregation]") {
    SECTION("Group bars by time period") {
        std::vector<BarData> bars;
        auto base_time = now();
        
        // Create bars with different timestamps
        for (int i = 0; i < 10; ++i) {
            BarData bar("000001.SZ", base_time + std::chrono::minutes(i), BarSize::Minute1, BarType::Candle);
            bar.open = 10.0;
            bar.high = 10.1;
            bar.low = 9.9;
            bar.close = 10.0 + (i % 3) * 0.1;  // Cyclical pattern
            bar.volume = 1000;
            bars.push_back(bar);
        }
        
        // Group by 5-minute periods
        std::vector<std::vector<BarData>> groups;
        for (size_t i = 0; i < bars.size(); i += 5) {
            std::vector<BarData> group;
            for (size_t j = i; j < std::min(i + 5, bars.size()); ++j) {
                group.push_back(bars[j]);
            }
            groups.push_back(group);
        }
        
        REQUIRE(groups.size() == 2);  // 10 bars / 5 = 2 groups
        REQUIRE(groups[0].size() == 5);
        REQUIRE(groups[1].size() == 5);
        
        // Verify time ordering within groups
        for (const auto& group : groups) {
            for (size_t i = 1; i < group.size(); ++i) {
                REQUIRE(group[i].timestamp > group[i-1].timestamp);
            }
        }
    }
}
