cmake_minimum_required(VERSION 3.20)
project(QuantServices VERSION 1.0.0 LANGUAGES CXX)

# Set C++20 standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set UTF-8 encoding
if(MSVC)
    add_compile_options(/utf-8)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
endif()

# Build configuration
set(CMAKE_CONFIGURATION_TYPES "Debug;Release" CACHE STRING "" FORCE)

# Output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Find packages
find_package(Threads REQUIRED)
find_package(Boost REQUIRED COMPONENTS system thread filesystem json)

# Include FetchContent for dependencies
include(FetchContent)

# spdlog for logging
FetchContent_Declare(
    spdlog
    GIT_REPOSITORY https://github.com/gabime/spdlog.git
    GIT_TAG v1.12.0
)

# nlohmann/json for JSON processing
FetchContent_Declare(
    nlohmann_json
    GIT_REPOSITORY https://github.com/nlohmann/json.git
    GIT_TAG v3.11.2
)

# fmt for formatting
FetchContent_Declare(
    fmt
    GIT_REPOSITORY https://github.com/fmtlib/fmt.git
    GIT_TAG 10.1.1
)

# Make dependencies available
FetchContent_MakeAvailable(spdlog nlohmann_json fmt)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../DataHub_Modern/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../TradingSvr_Modern/include
)

# Source files
set(QUANTSERVICES_SOURCES
    src/ServiceManager.cpp
    src/RestApiServer.cpp
    src/ConfigManager.cpp
    src/HealthMonitor.cpp
    src/main.cpp
    src/api/DataHubEndpoints.cpp
    src/api/TradingEndpoints.cpp
    src/api/SystemEndpoints.cpp
)

set(QUANTSERVICES_HEADERS
    include/quantservices/QuantServices.h
    include/quantservices/ServiceManager.h
    include/quantservices/RestApiServer.h
    include/quantservices/ConfigManager.h
    include/quantservices/HealthMonitor.h
    include/quantservices/api/DataHubEndpoints.h
    include/quantservices/api/TradingEndpoints.h
    include/quantservices/api/SystemEndpoints.h
)

# Create main executable
add_executable(${PROJECT_NAME} ${QUANTSERVICES_SOURCES} ${QUANTSERVICES_HEADERS})

# Link libraries
target_link_libraries(${PROJECT_NAME}
    PRIVATE
        Threads::Threads
        Boost::system
        Boost::thread
        Boost::filesystem
        Boost::json
        spdlog::spdlog
        nlohmann_json::nlohmann_json
        fmt::fmt
)

# Try to link DataHub_Modern and TradingSvr_Modern if available
find_library(DATAHUB_MODERN_LIB 
    NAMES datahub_core DataHub_Modern
    PATHS ${CMAKE_CURRENT_SOURCE_DIR}/../DataHub_Modern/build/lib
          ${CMAKE_CURRENT_SOURCE_DIR}/../DataHub_Modern/build
    NO_DEFAULT_PATH
)

find_library(TRADINGSVR_MODERN_LIB 
    NAMES TradingSvr_Modern
    PATHS ${CMAKE_CURRENT_SOURCE_DIR}/../TradingSvr_Modern/build/lib
          ${CMAKE_CURRENT_SOURCE_DIR}/../TradingSvr_Modern/build
    NO_DEFAULT_PATH
)

if(DATAHUB_MODERN_LIB)
    target_link_libraries(${PROJECT_NAME} PRIVATE ${DATAHUB_MODERN_LIB})
    message(STATUS "Found DataHub_Modern library: ${DATAHUB_MODERN_LIB}")
else()
    message(WARNING "DataHub_Modern library not found. Please build DataHub_Modern first.")
endif()

if(TRADINGSVR_MODERN_LIB)
    target_link_libraries(${PROJECT_NAME} PRIVATE ${TRADINGSVR_MODERN_LIB})
    message(STATUS "Found TradingSvr_Modern library: ${TRADINGSVR_MODERN_LIB}")
else()
    message(WARNING "TradingSvr_Modern library not found. Please build TradingSvr_Modern first.")
endif()

# Compiler-specific options
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE
        /W4 /WX- /bigobj
        /permissive-
        /Zc:__cplusplus
        /MP
    )
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        WIN32_LEAN_AND_MEAN
        NOMINMAX
        _WIN32_WINNT=0x0A00
    )
else()
    target_compile_options(${PROJECT_NAME} PRIVATE
        -Wall -Wextra -Wpedantic
        -Wno-unused-parameter
    )
endif()

# Debug/Release specific settings
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(${PROJECT_NAME} PRIVATE DEBUG)
    if(MSVC)
        target_compile_options(${PROJECT_NAME} PRIVATE /Od /Zi)
    else()
        target_compile_options(${PROJECT_NAME} PRIVATE -O0 -g)
    endif()
else()
    target_compile_definitions(${PROJECT_NAME} PRIVATE NDEBUG)
    if(MSVC)
        target_compile_options(${PROJECT_NAME} PRIVATE /O2)
    else()
        target_compile_options(${PROJECT_NAME} PRIVATE -O3 -DNDEBUG)
    endif()
endif()

# Enable testing
option(ENABLE_TESTS "Enable unit tests" ON)
if(ENABLE_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# Enable examples
option(ENABLE_EXAMPLES "Enable examples" ON)
if(ENABLE_EXAMPLES)
    add_subdirectory(examples)
endif()

# Installation
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

install(DIRECTORY include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h"
)

install(DIRECTORY config/
    DESTINATION config
    FILES_MATCHING PATTERN "*.json"
)
