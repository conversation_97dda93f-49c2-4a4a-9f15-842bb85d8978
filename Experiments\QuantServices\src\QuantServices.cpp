/**
 * @file QuantServices.cpp
 * @brief Implementation of main QuantServices application class
 * <AUTHOR> Team
 * @date 2024
 */

#include "quantservices/QuantServices.h"
#include "quantservices/api/DataHubEndpoints.h"
#include "quantservices/api/TradingEndpoints.h"
#include "quantservices/api/SystemEndpoints.h"
#include <spdlog/spdlog.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <csignal>
#include <fstream>
#include <filesystem>
#include <iomanip>
#include <sstream>
#include <random>

namespace QuantServices {

// ServiceConfig implementation

Json ServiceConfig::to_json() const {
    Json j;
    j["server"] = {
        {"host", server_host},
        {"port", server_port},
        {"worker_threads", worker_threads}
    };
    j["services"] = {
        {"datahub", {
            {"enabled", enable_datahub},
            {"config_file", datahub_config_file}
        }},
        {"trading", {
            {"enabled", enable_trading},
            {"config_file", trading_config_file}
        }}
    };
    j["logging"] = {
        {"level", log_level},
        {"file", log_file},
        {"console", log_to_console}
    };
    j["health_monitoring"] = {
        {"check_interval_seconds", health_check_interval.count()}
    };
    j["api"] = {
        {"cors", {
            {"enabled", enable_cors},
            {"allowed_origins", allowed_origins}
        }},
        {"max_request_size", max_request_size},
        {"auth", {
            {"enabled", enable_auth},
            {"api_key", api_key}
        }}
    };
    return j;
}

ServiceConfig ServiceConfig::from_json(const Json& j) {
    ServiceConfig config;
    
    if (j.contains("server")) {
        const auto& server = j["server"];
        if (server.contains("host")) config.server_host = server["host"];
        if (server.contains("port")) config.server_port = server["port"];
        if (server.contains("worker_threads")) config.worker_threads = server["worker_threads"];
    }
    
    if (j.contains("services")) {
        const auto& services = j["services"];
        if (services.contains("datahub")) {
            const auto& datahub = services["datahub"];
            if (datahub.contains("enabled")) config.enable_datahub = datahub["enabled"];
            if (datahub.contains("config_file")) config.datahub_config_file = datahub["config_file"];
        }
        if (services.contains("trading")) {
            const auto& trading = services["trading"];
            if (trading.contains("enabled")) config.enable_trading = trading["enabled"];
            if (trading.contains("config_file")) config.trading_config_file = trading["config_file"];
        }
    }
    
    if (j.contains("logging")) {
        const auto& logging = j["logging"];
        if (logging.contains("level")) config.log_level = logging["level"];
        if (logging.contains("file")) config.log_file = logging["file"];
        if (logging.contains("console")) config.log_to_console = logging["console"];
    }
    
    if (j.contains("health_monitoring")) {
        const auto& health = j["health_monitoring"];
        if (health.contains("check_interval_seconds")) {
            config.health_check_interval = std::chrono::seconds(health["check_interval_seconds"].get<int>());
        }
    }
    
    if (j.contains("api")) {
        const auto& api = j["api"];
        if (api.contains("cors")) {
            const auto& cors = api["cors"];
            if (cors.contains("enabled")) config.enable_cors = cors["enabled"];
            if (cors.contains("allowed_origins")) {
                config.allowed_origins = cors["allowed_origins"].get<std::vector<std::string>>();
            }
        }
        if (api.contains("max_request_size")) config.max_request_size = api["max_request_size"];
        if (api.contains("auth")) {
            const auto& auth = api["auth"];
            if (auth.contains("enabled")) config.enable_auth = auth["enabled"];
            if (auth.contains("api_key")) config.api_key = auth["api_key"];
        }
    }
    
    return config;
}

// QuantServicesApp implementation

QuantServicesApp::QuantServicesApp(const ServiceConfig& config)
    : config_(config)
    , status_(ServiceStatus::Stopped) {
}

QuantServicesApp::~QuantServicesApp() {
    if (status_ != ServiceStatus::Stopped) {
        stop();
    }
}

bool QuantServicesApp::initialize() {
    try {
        set_status(ServiceStatus::Starting);
        
        // Setup logging first
        if (!setup_logging()) {
            return false;
        }
        
        logger_->info("Initializing QuantServices application...");
        
        // Create configuration manager
        config_manager_ = std::make_shared<ConfigManager>();
        config_manager_->load_from_json(config_.to_json());
        
        // Create service manager
        service_manager_ = std::make_shared<ServiceManager>();
        
        // Create health monitor
        HealthMonitorConfig health_config;
        health_config.check_interval = config_.health_check_interval;
        health_monitor_ = std::make_shared<HealthMonitor>(health_config);
        
        // Create REST API server
        ServerConfig server_config;
        server_config.host = config_.server_host;
        server_config.port = config_.server_port;
        server_config.thread_count = config_.worker_threads;
        server_config.enable_cors = config_.enable_cors;
        server_config.allowed_origins = config_.allowed_origins;
        server_config.max_request_size = config_.max_request_size;
        
        api_server_ = std::make_shared<RestApiServer>(server_config);
        
        // Register API endpoints
        register_api_endpoints();
        
        // Initialize services
        Json services_config = {
            {"datahub", {
                {"enabled", config_.enable_datahub},
                {"config_file", config_.datahub_config_file}
            }},
            {"trading", {
                {"enabled", config_.enable_trading},
                {"config_file", config_.trading_config_file}
            }}
        };
        
        auto init_future = service_manager_->initialize(services_config);
        if (!init_future.get()) {
            logger_->error("Failed to initialize services");
            return false;
        }
        
        // Setup signal handlers
        setup_signal_handlers();
        
        logger_->info("QuantServices application initialized successfully");
        set_status(ServiceStatus::Stopped); // Ready to start
        return true;
        
    } catch (const std::exception& e) {
        if (logger_) {
            logger_->error("Failed to initialize application: {}", e.what());
        }
        set_status(ServiceStatus::Error);
        return false;
    }
}

bool QuantServicesApp::start() {
    try {
        if (status_ == ServiceStatus::Running) {
            logger_->warn("Application is already running");
            return true;
        }
        
        set_status(ServiceStatus::Starting);
        logger_->info("Starting QuantServices application...");
        
        // Start health monitor
        if (!health_monitor_->start()) {
            logger_->error("Failed to start health monitor");
            return false;
        }
        
        // Start services
        auto services_future = service_manager_->start_all();
        if (!services_future.get()) {
            logger_->error("Failed to start services");
            return false;
        }
        
        // Start API server
        auto server_future = api_server_->start();
        if (!server_future.get()) {
            logger_->error("Failed to start API server");
            return false;
        }
        
        set_status(ServiceStatus::Running);
        logger_->info("QuantServices application started successfully");
        logger_->info("API server listening on {}:{}", config_.server_host, config_.server_port);
        
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to start application: {}", e.what());
        set_status(ServiceStatus::Error);
        return false;
    }
}

void QuantServicesApp::stop() {
    try {
        if (status_ == ServiceStatus::Stopped) {
            return;
        }
        
        set_status(ServiceStatus::Stopping);
        logger_->info("Stopping QuantServices application...");
        
        // Stop API server
        if (api_server_) {
            auto server_future = api_server_->stop();
            server_future.wait();
        }
        
        // Stop services
        if (service_manager_) {
            auto services_future = service_manager_->stop_all();
            services_future.wait();
        }
        
        // Stop health monitor
        if (health_monitor_) {
            health_monitor_->stop();
        }
        
        set_status(ServiceStatus::Stopped);
        logger_->info("QuantServices application stopped");
        
    } catch (const std::exception& e) {
        if (logger_) {
            logger_->error("Exception during shutdown: {}", e.what());
        }
        set_status(ServiceStatus::Error);
    }
}

int QuantServicesApp::run() {
    try {
        if (status_ != ServiceStatus::Running) {
            logger_->error("Application is not running");
            return 1;
        }
        
        logger_->info("QuantServices application is running. Press Ctrl+C to stop.");
        
        // Wait for shutdown signal
        while (status_ == ServiceStatus::Running) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        
        return 0;
        
    } catch (const std::exception& e) {
        if (logger_) {
            logger_->error("Exception in run loop: {}", e.what());
        }
        return 1;
    }
}

ServiceStatus QuantServicesApp::get_status() const {
    std::lock_guard lock(status_mutex_);
    return status_;
}

std::shared_ptr<ServiceManager> QuantServicesApp::get_service_manager() const {
    return service_manager_;
}

std::shared_ptr<RestApiServer> QuantServicesApp::get_api_server() const {
    return api_server_;
}

const ServiceConfig& QuantServicesApp::get_config() const {
    return config_;
}

bool QuantServicesApp::update_config(const ServiceConfig& new_config) {
    try {
        config_ = new_config;
        
        if (config_manager_) {
            config_manager_->load_from_json(config_.to_json());
        }
        
        logger_->info("Configuration updated");
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to update configuration: {}", e.what());
        return false;
    }
}

Json QuantServicesApp::get_health_status() const {
    if (health_monitor_) {
        return health_monitor_->get_health_summary();
    }
    return Json{{"status", "unknown"}};
}

Json QuantServicesApp::get_system_metrics() const {
    if (health_monitor_) {
        auto metrics = health_monitor_->get_system_metrics();
        return metrics.to_json();
    }
    return Json{{"error", "Health monitor not available"}};
}

// Private methods implementation

bool QuantServicesApp::setup_logging() {
    try {
        // Create logger
        std::vector<spdlog::sink_ptr> sinks;

        // Console sink
        if (config_.log_to_console) {
            auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
            console_sink->set_level(spdlog::level::from_str(config_.log_level));
            sinks.push_back(console_sink);
        }

        // File sink
        if (!config_.log_file.empty()) {
            // Create log directory if it doesn't exist
            auto log_path = std::filesystem::path(config_.log_file);
            auto log_dir = log_path.parent_path();
            if (!log_dir.empty() && !std::filesystem::exists(log_dir)) {
                std::filesystem::create_directories(log_dir);
            }

            auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
                config_.log_file, 1024 * 1024 * 100, 10); // 100MB, 10 files
            file_sink->set_level(spdlog::level::from_str(config_.log_level));
            sinks.push_back(file_sink);
        }

        // Create logger
        logger_ = std::make_shared<spdlog::logger>("quantservices", sinks.begin(), sinks.end());
        logger_->set_level(spdlog::level::from_str(config_.log_level));
        logger_->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%l] [%n] %v");

        // Register as default logger
        spdlog::register_logger(logger_);
        spdlog::set_default_logger(logger_);

        logger_->info("Logging system initialized");
        return true;

    } catch (const std::exception& e) {
        std::cerr << "Failed to setup logging: " << e.what() << std::endl;
        return false;
    }
}

bool QuantServicesApp::setup_signal_handlers() {
    try {
        // Signal handlers are typically set up in main.cpp
        // This is a placeholder for any additional signal handling setup
        logger_->debug("Signal handlers setup completed");
        return true;

    } catch (const std::exception& e) {
        logger_->error("Failed to setup signal handlers: {}", e.what());
        return false;
    }
}

void QuantServicesApp::handle_shutdown_signal() {
    logger_->info("Shutdown signal received");
    stop();
}

void QuantServicesApp::set_status(ServiceStatus status) {
    std::lock_guard lock(status_mutex_);
    status_ = status;
}

void QuantServicesApp::register_api_endpoints() {
    if (!api_server_) {
        return;
    }

    logger_->info("Registering API endpoints...");

    // Register system endpoints
    auto system_endpoints = std::make_shared<SystemEndpoints>(
        service_manager_, health_monitor_, config_manager_);
    system_endpoints->register_endpoints(*api_server_);

    // Register DataHub endpoints if enabled
    if (config_.enable_datahub && service_manager_) {
        auto datahub_manager = service_manager_->get_datahub_manager();
        auto datahub_api = service_manager_->get_datahub_api();

        auto datahub_endpoints = std::make_shared<DataHubEndpoints>(datahub_manager, datahub_api);
        datahub_endpoints->register_endpoints(*api_server_);
    }

    // Register Trading endpoints if enabled
    if (config_.enable_trading && service_manager_) {
        auto trading_server = service_manager_->get_trading_server();

        auto trading_endpoints = std::make_shared<TradingEndpoints>(trading_server);
        trading_endpoints->register_endpoints(*api_server_);
    }

    // Add middleware
    if (config_.enable_cors) {
        api_server_->register_middleware(Middleware::cors(config_.allowed_origins));
    }

    api_server_->register_middleware(Middleware::request_logging());
    api_server_->register_middleware(Middleware::request_size_limit(config_.max_request_size));

    if (config_.enable_auth && !config_.api_key.empty()) {
        api_server_->register_middleware(Middleware::api_key_auth(config_.api_key));
    }

    logger_->info("API endpoints registered successfully");
}

// Utility functions implementation

std::string to_string(ServiceStatus status) {
    switch (status) {
        case ServiceStatus::Stopped: return "stopped";
        case ServiceStatus::Starting: return "starting";
        case ServiceStatus::Running: return "running";
        case ServiceStatus::Stopping: return "stopping";
        case ServiceStatus::Error: return "error";
        default: return "unknown";
    }
}

// Factory namespace implementation

namespace Factory {

std::unique_ptr<QuantServicesApp> create_default_app(uint16_t port) {
    ServiceConfig config;
    config.server_port = port;
    return std::make_unique<QuantServicesApp>(config);
}

std::unique_ptr<QuantServicesApp> create_app_from_config(const std::string& config_file) {
    try {
        if (!std::filesystem::exists(config_file)) {
            throw std::runtime_error("Configuration file not found: " + config_file);
        }

        std::ifstream file(config_file);
        Json config_json;
        file >> config_json;

        auto config = ServiceConfig::from_json(config_json);
        return std::make_unique<QuantServicesApp>(config);

    } catch (const std::exception& e) {
        throw std::runtime_error("Failed to create app from config: " + std::string(e.what()));
    }
}

std::unique_ptr<QuantServicesApp> create_app_with_config(const ServiceConfig& config) {
    return std::make_unique<QuantServicesApp>(config);
}

} // namespace Factory

// Utils namespace implementation

namespace Utils {

std::string get_current_timestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count() << 'Z';
    return ss.str();
}

TimePoint parse_timestamp(const std::string& timestamp) {
    std::tm tm = {};
    std::istringstream ss(timestamp);
    ss >> std::get_time(&tm, "%Y-%m-%dT%H:%M:%S");

    if (ss.fail()) {
        throw std::invalid_argument("Invalid timestamp format");
    }

    return std::chrono::system_clock::from_time_t(std::mktime(&tm));
}

std::string generate_uuid() {
    // Simple UUID generation (not cryptographically secure)
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(0, 15);

    std::stringstream ss;
    ss << std::hex;
    for (int i = 0; i < 8; ++i) ss << dis(gen);
    ss << "-";
    for (int i = 0; i < 4; ++i) ss << dis(gen);
    ss << "-4"; // Version 4 UUID
    for (int i = 0; i < 3; ++i) ss << dis(gen);
    ss << "-";
    ss << std::hex << (8 + (dis(gen) % 4)); // Variant bits
    for (int i = 0; i < 3; ++i) ss << dis(gen);
    ss << "-";
    for (int i = 0; i < 12; ++i) ss << dis(gen);

    return ss.str();
}

bool validate_json_schema(const Json& data, const Json& schema) {
    // Basic JSON schema validation
    // This is a simplified implementation
    try {
        if (schema.contains("type")) {
            std::string expected_type = schema["type"];

            if (expected_type == "object" && !data.is_object()) return false;
            if (expected_type == "array" && !data.is_array()) return false;
            if (expected_type == "string" && !data.is_string()) return false;
            if (expected_type == "number" && !data.is_number()) return false;
            if (expected_type == "boolean" && !data.is_boolean()) return false;
        }

        if (schema.contains("required") && schema["required"].is_array()) {
            for (const auto& required_field : schema["required"]) {
                if (!data.contains(required_field.get<std::string>())) {
                    return false;
                }
            }
        }

        return true;

    } catch (const std::exception&) {
        return false;
    }
}

Json create_error_response(const std::string& error, const std::string& details) {
    Json response = {
        {"success", false},
        {"error", {
            {"message", error},
            {"details", details}
        }},
        {"timestamp", std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count()}
    };
    return response;
}

Json create_success_response(const Json& data) {
    Json response = {
        {"success", true},
        {"data", data},
        {"timestamp", std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count()}
    };
    return response;
}

} // namespace Utils

} // namespace QuantServices
