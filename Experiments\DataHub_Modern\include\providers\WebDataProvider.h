#pragma once

#include "IMarketDataProvider.h"
#include "../core/Types.h"
#include <memory>
#include <string>
#include <vector>
#include <thread>
#include <atomic>
#include <chrono>
#include <unordered_map>
#include <unordered_set>
#include <concurrentqueue.h>

namespace DataHub::Providers {

// Web?????????
enum class WebDataSource : std::uint8_t {
    Sina = 0,       // ??????
    Tencent = 1,    // ??????
    NetEase = 2,    // ??????
    Mixed = 3       // ????????
};

// Web????????
struct WebDataConfig : public ProviderConfig {
    WebDataSource data_source{WebDataSource::Sina};
    std::string base_url;
    std::chrono::milliseconds update_interval{3000};    // ??????
    std::chrono::milliseconds request_timeout{5000};    // ?????
    std::size_t max_symbols_per_request{100};           // ??????????????
    std::size_t max_concurrent_requests{5};             // ??????????
    bool enable_cache{true};                            // ???????
    std::chrono::seconds cache_ttl{30};                 // ????TTL
    bool only_update_subscribed{true};                  // ???????????
    
    // HTTP???????
    std::string user_agent{"DataHub/1.0"};
    std::unordered_map<std::string, std::string> headers;
    
    WebDataConfig() {
        type = ProviderType::WebData_Stock;
        name = "WebData_Stock_Provider";
    }
};

// HTTP???????
class IHttpClient {
public:
    virtual ~IHttpClient() = default;
    
    virtual Core::Result<std::string> get(const std::string& url, 
                                         std::chrono::milliseconds timeout = std::chrono::milliseconds(5000)) = 0;
    virtual Core::Result<std::string> post(const std::string& url, 
                                          const std::string& data,
                                          std::chrono::milliseconds timeout = std::chrono::milliseconds(5000)) = 0;
    virtual void set_headers(const std::unordered_map<std::string, std::string>& headers) = 0;
    virtual void set_user_agent(const std::string& user_agent) = 0;
};

// Web????????????
class IWebDataParser {
public:
    virtual ~IWebDataParser() = default;
    
    virtual Core::Result<std::vector<Core::QuoteData>> parse_quotes(
        const std::string& data, 
        const std::vector<Core::Symbol>& symbols) = 0;
    
    virtual Core::Result<std::vector<Core::BarData>> parse_bars(
        const std::string& data,
        const Core::Symbol& symbol,
        Core::BarSize bar_size) = 0;
    
    virtual std::string build_quote_url(const std::vector<Core::Symbol>& symbols) = 0;
    virtual std::string build_history_url(const Core::Symbol& symbol, 
                                         Core::BarSize bar_size,
                                         const Core::Timestamp& start_time,
                                         const Core::Timestamp& end_time) = 0;
};

// Web????????
class WebDataProvider : public MarketDataProviderBase {
public:
    explicit WebDataProvider();
    ~WebDataProvider() override;
    
    // IMarketDataProvider interface
    Core::Result<void> connect() override;
    Core::Result<void> disconnect() override;
    
    Core::Result<void> subscribe_quote(const Core::Symbol& symbol) override;
    Core::Result<void> subscribe_quotes(const std::vector<Core::Symbol>& symbols) override;
    Core::Result<void> unsubscribe_quote(const Core::Symbol& symbol) override;
    Core::Result<void> unsubscribe_all() override;
    
    Core::Result<Core::QuoteData> get_latest_quote(const Core::Symbol& symbol) override;
    Core::Result<std::vector<Core::QuoteData>> get_quotes(
        const std::vector<Core::Symbol>& symbols) override;
    
    std::future<Core::Result<Core::QuoteData>> get_latest_quote_async(
        const Core::Symbol& symbol) override;
    std::future<Core::Result<std::vector<Core::QuoteData>>> get_quotes_async(
        const std::vector<Core::Symbol>& symbols) override;
    
    Core::Result<bool> health_check() override;
    std::string get_version() const noexcept override;
    
    // Web???????
    Core::Result<std::vector<Core::BarData>> get_history_bars(
        const Core::Symbol& symbol,
        Core::BarSize bar_size,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time);
    
    Core::Result<void> set_data_source(WebDataSource source);
    Core::Result<void> set_update_interval(std::chrono::milliseconds interval);

protected:
    // MarketDataProviderBase interface
    Core::Result<void> do_connect() override;
    Core::Result<void> do_disconnect() override;
    Core::Result<void> do_subscribe(const Core::Symbol& symbol) override;
    Core::Result<void> do_unsubscribe(const Core::Symbol& symbol) override;
    Core::Result<void> do_start() override;
    Core::Result<void> do_stop() override;

private:
    // ????
    std::shared_ptr<WebDataConfig> web_config_;
    
    // HTTP???????????
    std::unique_ptr<IHttpClient> http_client_;
    std::unique_ptr<IWebDataParser> data_parser_;
    
    // ???????
    std::unordered_set<Core::Symbol> subscribed_symbols_;
    mutable std::shared_mutex symbols_mutex_;
    
    // ???????
    void start_data_update();
    void stop_data_update();
    void update_quotes();
    void update_quotes_batch(const std::vector<Core::Symbol>& symbols);
    
    // ???????
    struct CachedQuote {
        Core::QuoteData quote;
        std::chrono::system_clock::time_point timestamp;
    };
    std::unordered_map<Core::Symbol, CachedQuote> quote_cache_;
    mutable std::shared_mutex cache_mutex_;
    
    bool is_cache_valid(const CachedQuote& cached) const;
    void update_cache(const Core::Symbol& symbol, const Core::QuoteData& quote);
    std::optional<Core::QuoteData> get_from_cache(const Core::Symbol& symbol) const;
    
    // ???????
    std::thread update_thread_;
    std::atomic<bool> stop_update_{false};
    
    // ???????
    std::string symbol_to_web_code(const Core::Symbol& symbol) const;
    Core::Symbol web_code_to_symbol(const std::string& web_code) const;
    
    // ??????????
    std::vector<std::vector<Core::Symbol>> split_symbols_into_batches(
        const std::vector<Core::Symbol>& symbols) const;
    
    // ????????????
    Core::Result<std::string> request_with_retry(const std::string& url, int max_retries = 3);
    
    // ??????
    std::atomic<std::size_t> request_count_{0};
    std::atomic<std::size_t> cache_hit_count_{0};
    std::atomic<std::size_t> cache_miss_count_{0};
    
public:
    // ?????????
    std::size_t get_request_count() const noexcept { return request_count_; }
    std::size_t get_cache_hit_count() const noexcept { return cache_hit_count_; }
    std::size_t get_cache_miss_count() const noexcept { return cache_miss_count_; }
    double get_cache_hit_ratio() const noexcept;
};

// ?????????????
class SinaDataParser : public IWebDataParser {
public:
    Core::Result<std::vector<Core::QuoteData>> parse_quotes(
        const std::string& data, 
        const std::vector<Core::Symbol>& symbols) override;
    
    Core::Result<std::vector<Core::BarData>> parse_bars(
        const std::string& data,
        const Core::Symbol& symbol,
        Core::BarSize bar_size) override;
    
    std::string build_quote_url(const std::vector<Core::Symbol>& symbols) override;
    std::string build_history_url(const Core::Symbol& symbol, 
                                 Core::BarSize bar_size,
                                 const Core::Timestamp& start_time,
                                 const Core::Timestamp& end_time) override;

private:
    std::string symbol_to_sina_code(const Core::Symbol& symbol) const;
    Core::Symbol sina_code_to_symbol(const std::string& sina_code) const;
    Core::QuoteData parse_sina_quote_line(const std::string& line) const;
};

// ????????????
class TencentDataParser : public IWebDataParser {
public:
    Core::Result<std::vector<Core::QuoteData>> parse_quotes(
        const std::string& data, 
        const std::vector<Core::Symbol>& symbols) override;
    
    Core::Result<std::vector<Core::BarData>> parse_bars(
        const std::string& data,
        const Core::Symbol& symbol,
        Core::BarSize bar_size) override;
    
    std::string build_quote_url(const std::vector<Core::Symbol>& symbols) override;
    std::string build_history_url(const Core::Symbol& symbol, 
                                 Core::BarSize bar_size,
                                 const Core::Timestamp& start_time,
                                 const Core::Timestamp& end_time) override;

private:
    std::string symbol_to_tencent_code(const Core::Symbol& symbol) const;
    Core::Symbol tencent_code_to_symbol(const std::string& tencent_code) const;
    Core::QuoteData parse_tencent_quote_line(const std::string& line) const;
};

// Web???????
class WebDataProviderFactory : public IProviderFactory {
public:
    std::unique_ptr<IMarketDataProvider> create_provider(
        ProviderType type, 
        std::shared_ptr<ProviderConfig> config) override;
    
    std::vector<ProviderType> get_supported_types() const override;
    bool supports_type(ProviderType type) const override;
    
    // Web???????????
    static std::unique_ptr<WebDataProvider> create_web_provider(
        std::shared_ptr<WebDataConfig> config);
};

} // namespace DataHub::Providers
