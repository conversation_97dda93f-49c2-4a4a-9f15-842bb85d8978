/**
 * @file ServiceManager.h
 * @brief Service manager for coordinating DataHub and Trading services
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include <memory>
#include <string>
#include <atomic>
#include <mutex>
#include <future>
#include <functional>
#include <unordered_map>

#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>

// Forward declarations for DataHub and Trading services
namespace DataHub {
    namespace Services {
        class IDataHubManager;
    }
    namespace API {
        class DataHubAPI;
    }
}

namespace RoboQuant::Trading {
    class TradingServer;
}

namespace QuantServices {

using Json = nlohmann::json;

/**
 * @brief Service status enumeration
 */
enum class ServiceStatus {
    Stopped,
    Starting,
    Running,
    Stopping,
    Error
};

/**
 * @brief Service information structure
 */
struct ServiceInfo {
    std::string name;
    std::string version;
    ServiceStatus status;
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point last_heartbeat;
    Json metadata;
    
    Json to_json() const;
};

/**
 * @brief Service event callback type
 */
using ServiceEventCallback = std::function<void(const std::string& service_name, 
                                               const std::string& event_type, 
                                               const Json& event_data)>;

/**
 * @brief Main service manager class
 */
class ServiceManager {
public:
    /**
     * @brief Constructor
     */
    explicit ServiceManager();
    
    /**
     * @brief Destructor
     */
    ~ServiceManager();
    
    // Non-copyable, non-movable
    ServiceManager(const ServiceManager&) = delete;
    ServiceManager& operator=(const ServiceManager&) = delete;
    ServiceManager(ServiceManager&&) = delete;
    ServiceManager& operator=(ServiceManager&&) = delete;
    
    /**
     * @brief Initialize all services
     */
    std::future<bool> initialize(const Json& config);
    
    /**
     * @brief Start all services
     */
    std::future<bool> start_all();
    
    /**
     * @brief Stop all services
     */
    std::future<void> stop_all();
    
    /**
     * @brief Start specific service
     */
    std::future<bool> start_service(const std::string& service_name);
    
    /**
     * @brief Stop specific service
     */
    std::future<bool> stop_service(const std::string& service_name);
    
    /**
     * @brief Restart specific service
     */
    std::future<bool> restart_service(const std::string& service_name);
    
    /**
     * @brief Get service status
     */
    ServiceStatus get_service_status(const std::string& service_name) const;
    
    /**
     * @brief Get all services status
     */
    std::unordered_map<std::string, ServiceInfo> get_all_services_status() const;
    
    /**
     * @brief Check if all services are running
     */
    bool are_all_services_running() const;
    
    /**
     * @brief Get DataHub manager
     */
    std::shared_ptr<DataHub::Services::IDataHubManager> get_datahub_manager() const;
    
    /**
     * @brief Get DataHub API
     */
    std::shared_ptr<DataHub::API::DataHubAPI> get_datahub_api() const;
    
    /**
     * @brief Get Trading server
     */
    std::shared_ptr<RoboQuant::Trading::TradingServer> get_trading_server() const;
    
    /**
     * @brief Set service event callback
     */
    void set_service_event_callback(ServiceEventCallback callback);
    
    /**
     * @brief Get service health metrics
     */
    Json get_health_metrics() const;
    
    /**
     * @brief Get service performance metrics
     */
    Json get_performance_metrics() const;
    
    /**
     * @brief Enable/disable service
     */
    void enable_service(const std::string& service_name, bool enabled);
    
    /**
     * @brief Check if service is enabled
     */
    bool is_service_enabled(const std::string& service_name) const;

private:
    // Service instances
    std::shared_ptr<DataHub::Services::IDataHubManager> datahub_manager_;
    std::shared_ptr<DataHub::API::DataHubAPI> datahub_api_;
    std::shared_ptr<RoboQuant::Trading::TradingServer> trading_server_;
    
    // Service status tracking
    mutable std::shared_mutex services_mutex_;
    std::unordered_map<std::string, ServiceInfo> services_info_;
    std::unordered_map<std::string, bool> services_enabled_;
    
    // Configuration
    Json config_;
    
    // Event handling
    ServiceEventCallback event_callback_;
    std::mutex event_callback_mutex_;
    
    // Logging
    std::shared_ptr<spdlog::logger> logger_;
    
    // Health monitoring
    std::atomic<bool> health_monitoring_active_{false};
    std::unique_ptr<std::thread> health_monitor_thread_;
    
    // Internal methods
    bool initialize_datahub(const Json& datahub_config);
    bool initialize_trading(const Json& trading_config);
    
    std::future<bool> start_datahub();
    std::future<bool> start_trading();
    
    std::future<bool> stop_datahub();
    std::future<bool> stop_trading();
    
    void update_service_info(const std::string& service_name, ServiceStatus status);
    void emit_service_event(const std::string& service_name, 
                           const std::string& event_type, 
                           const Json& event_data);
    
    void start_health_monitoring();
    void stop_health_monitoring();
    void health_monitor_loop();
    
    void check_datahub_health();
    void check_trading_health();
    
    // Service name constants
    static constexpr const char* DATAHUB_SERVICE = "datahub";
    static constexpr const char* TRADING_SERVICE = "trading";
};

/**
 * @brief Service manager builder for fluent configuration
 */
class ServiceManagerBuilder {
public:
    ServiceManagerBuilder() = default;
    
    ServiceManagerBuilder& with_datahub_config(const Json& config);
    ServiceManagerBuilder& with_trading_config(const Json& config);
    ServiceManagerBuilder& with_health_monitoring(bool enabled);
    ServiceManagerBuilder& with_event_callback(ServiceEventCallback callback);
    
    std::unique_ptr<ServiceManager> build();

private:
    Json datahub_config_;
    Json trading_config_;
    bool health_monitoring_enabled_{true};
    ServiceEventCallback event_callback_;
};

} // namespace QuantServices
