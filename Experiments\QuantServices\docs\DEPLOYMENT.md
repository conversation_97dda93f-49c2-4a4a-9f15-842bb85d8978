# QuantServices Deployment Guide

## Prerequisites

### System Requirements

- **Operating System**: Windows 10/11, Linux (Ubuntu 20.04+), macOS 10.15+
- **CPU**: 4+ cores recommended
- **Memory**: 8GB+ RAM recommended
- **Storage**: 10GB+ free space
- **Network**: Internet connection for dependencies

### Development Tools

- **C++ Compiler**: 
  - Windows: Visual Studio 2019+ or MinGW-w64
  - Linux: GCC 10+ or Clang 12+
  - macOS: Xcode 12+ or Clang 12+
- **CMake**: 3.20 or later
- **Git**: For source code management

### Dependencies

#### Required Libraries
- **Boost**: 1.75+ (system, thread, filesystem, json)
- **nlohmann/json**: 3.11+
- **spdlog**: 1.12+
- **fmt**: 10.1+

#### Optional Libraries
- **DataHub_Modern**: For DataHub functionality
- **TradingSvr_Modern**: For Trading functionality
- **Google Test**: For unit testing
- **libcurl**: For HTTP client examples

## Building from Source

### 1. Clone Repository

```bash
git clone <repository-url>
cd QuantServices
```

### 2. Install Dependencies

#### Windows (vcpkg)
```powershell
# Install vcpkg if not already installed
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat
.\vcpkg integrate install

# Install dependencies
.\vcpkg install boost:x64-windows nlohmann-json:x64-windows spdlog:x64-windows fmt:x64-windows
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install -y build-essential cmake git
sudo apt install -y libboost-all-dev nlohmann-json3-dev libspdlog-dev libfmt-dev
```

#### macOS (Homebrew)
```bash
brew install cmake boost nlohmann-json spdlog fmt
```

### 3. Build DataHub_Modern and TradingSvr_Modern

First, build the required dependencies:

```bash
# Build DataHub_Modern
cd ../DataHub_Modern
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
cmake --build . --config Release

# Build TradingSvr_Modern
cd ../../TradingSvr_Modern
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
cmake --build . --config Release
```

### 4. Build QuantServices

```bash
cd ../../QuantServices
mkdir build && cd build

# Configure
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build
cmake --build . --config Release

# Optional: Build with tests and examples
cmake .. -DCMAKE_BUILD_TYPE=Release -DENABLE_TESTS=ON -DENABLE_EXAMPLES=ON
cmake --build . --config Release
```

### 5. Run Tests (Optional)

```bash
ctest --output-on-failure
```

## Configuration

### 1. Create Configuration Directory

```bash
mkdir -p config logs data
```

### 2. Create Configuration File

Copy the default configuration and modify as needed:

```bash
cp config/default.json config/production.json
```

Edit `config/production.json`:

```json
{
  "server": {
    "host": "0.0.0.0",
    "port": 8080,
    "worker_threads": 4
  },
  "services": {
    "datahub": {
      "enabled": true,
      "config_file": "config/datahub.json"
    },
    "trading": {
      "enabled": true,
      "config_file": "config/trading.json"
    }
  },
  "logging": {
    "level": "info",
    "file": "logs/quantservices.log",
    "console": false
  },
  "health_monitoring": {
    "enabled": true,
    "check_interval_seconds": 30
  }
}
```

### 3. Environment Variables

Set environment variables for sensitive configuration:

```bash
export QUANTSERVICES_API_KEY="your-secret-api-key"
export QUANTSERVICES_DATABASE_PASSWORD="your-db-password"
```

## Deployment Options

### Option 1: Direct Execution

```bash
# Run with default configuration
./bin/QuantServices

# Run with custom configuration
./bin/QuantServices -c config/production.json

# Run with specific port
./bin/QuantServices -p 9090

# Run with debug logging
./bin/QuantServices -l debug
```

### Option 2: Systemd Service (Linux)

Create service file `/etc/systemd/system/quantservices.service`:

```ini
[Unit]
Description=QuantServices API Server
After=network.target

[Service]
Type=simple
User=quantservices
Group=quantservices
WorkingDirectory=/opt/quantservices
ExecStart=/opt/quantservices/bin/QuantServices -c /opt/quantservices/config/production.json
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# Environment variables
Environment=QUANTSERVICES_API_KEY=your-secret-api-key

[Install]
WantedBy=multi-user.target
```

Enable and start the service:

```bash
sudo systemctl daemon-reload
sudo systemctl enable quantservices
sudo systemctl start quantservices
sudo systemctl status quantservices
```

### Option 3: Docker Deployment

Create `Dockerfile`:

```dockerfile
FROM ubuntu:22.04

# Install dependencies
RUN apt-get update && apt-get install -y \
    libboost-all-dev \
    nlohmann-json3-dev \
    libspdlog-dev \
    libfmt-dev \
    && rm -rf /var/lib/apt/lists/*

# Create user
RUN useradd -m -s /bin/bash quantservices

# Copy application
COPY --chown=quantservices:quantservices bin/ /app/bin/
COPY --chown=quantservices:quantservices config/ /app/config/

# Create directories
RUN mkdir -p /app/logs /app/data && \
    chown -R quantservices:quantservices /app

USER quantservices
WORKDIR /app

EXPOSE 8080

CMD ["./bin/QuantServices", "-c", "config/production.json"]
```

Build and run:

```bash
docker build -t quantservices:latest .
docker run -d -p 8080:8080 --name quantservices quantservices:latest
```

### Option 4: Docker Compose

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  quantservices:
    build: .
    ports:
      - "8080:8080"
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./data:/app/data
    environment:
      - QUANTSERVICES_API_KEY=${QUANTSERVICES_API_KEY}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v1/system/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - quantservices
    restart: unless-stopped
```

## Production Considerations

### Security

1. **Enable HTTPS**: Use reverse proxy (nginx) with SSL certificates
2. **API Authentication**: Enable API key authentication
3. **Firewall**: Restrict access to necessary ports only
4. **User Permissions**: Run service with limited user privileges
5. **Input Validation**: Ensure all API inputs are validated

### Performance

1. **Resource Allocation**: 
   - CPU: 4+ cores for production
   - Memory: 8GB+ RAM
   - Storage: SSD recommended for logs and data

2. **Configuration Tuning**:
   ```json
   {
     "server": {
       "worker_threads": 8,
       "max_request_size": 1048576
     },
     "health_monitoring": {
       "check_interval_seconds": 60,
       "metrics_interval_seconds": 300
     }
   }
   ```

3. **Load Balancing**: Use nginx or HAProxy for multiple instances

### Monitoring

1. **Health Checks**: Monitor `/api/v1/system/health` endpoint
2. **Metrics Collection**: Use `/api/v1/system/metrics` for monitoring
3. **Log Aggregation**: Centralize logs using ELK stack or similar
4. **Alerting**: Set up alerts for critical health status

### Backup and Recovery

1. **Configuration Backup**: Regularly backup configuration files
2. **Data Backup**: Backup data directory if using file-based storage
3. **Log Rotation**: Configure log rotation to prevent disk space issues

```bash
# Example log rotation configuration
/opt/quantservices/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 quantservices quantservices
    postrotate
        systemctl reload quantservices
    endscript
}
```

## Troubleshooting

### Common Issues

1. **Port Already in Use**:
   ```bash
   # Check what's using the port
   sudo netstat -tlnp | grep :8080
   # Change port in configuration or stop conflicting service
   ```

2. **Permission Denied**:
   ```bash
   # Check file permissions
   ls -la /opt/quantservices/
   # Fix permissions
   sudo chown -R quantservices:quantservices /opt/quantservices/
   ```

3. **Missing Dependencies**:
   ```bash
   # Check library dependencies
   ldd bin/QuantServices
   # Install missing libraries
   ```

4. **Configuration Errors**:
   ```bash
   # Validate JSON configuration
   python -m json.tool config/production.json
   # Check logs for specific errors
   tail -f logs/quantservices.log
   ```

### Log Analysis

Monitor logs for common patterns:

```bash
# Check for errors
grep -i error logs/quantservices.log

# Check for warnings
grep -i warning logs/quantservices.log

# Monitor real-time logs
tail -f logs/quantservices.log | grep -E "(ERROR|WARN|CRITICAL)"
```

### Performance Monitoring

```bash
# Monitor system resources
top -p $(pgrep QuantServices)

# Check memory usage
ps aux | grep QuantServices

# Monitor network connections
netstat -an | grep :8080
```

## Scaling

### Horizontal Scaling

1. **Load Balancer Configuration** (nginx):
   ```nginx
   upstream quantservices {
       server 127.0.0.1:8080;
       server 127.0.0.1:8081;
       server 127.0.0.1:8082;
   }
   
   server {
       listen 80;
       location /api/ {
           proxy_pass http://quantservices;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

2. **Multiple Instances**: Run multiple QuantServices instances on different ports

### Vertical Scaling

1. **Increase Resources**: Add more CPU cores and memory
2. **Tune Configuration**: Increase worker threads and buffer sizes
3. **Optimize Database**: Use faster storage and optimize queries

## Maintenance

### Regular Tasks

1. **Update Dependencies**: Keep libraries up to date
2. **Monitor Logs**: Check for errors and warnings
3. **Performance Review**: Analyze metrics and optimize
4. **Security Updates**: Apply security patches promptly
5. **Backup Verification**: Test backup and recovery procedures

### Upgrade Procedure

1. **Backup Current Installation**
2. **Test New Version** in staging environment
3. **Schedule Maintenance Window**
4. **Deploy New Version**
5. **Verify Functionality**
6. **Monitor for Issues**
