# QuantServices API Documentation

## Overview

QuantServices provides a unified REST API for accessing DataHub and Trading functionality. All endpoints return JSON responses and follow RESTful conventions.

### Base URL
```
http://localhost:8080/api/v1
```

### Response Format

All API responses follow this standard format:

```json
{
  "success": true,
  "data": { ... },
  "timestamp": 1640995200000
}
```

Error responses:
```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "details": "Additional error details",
    "code": 400
  },
  "timestamp": 1640995200000
}
```

### HTTP Status Codes

- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid request parameters
- `401 Unauthorized` - Authentication required
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Server error

## System Endpoints

### Get System Information
```
GET /system/info
```

Returns basic system information including version, build info, and component availability.

**Response:**
```json
{
  "success": true,
  "data": {
    "service": "QuantServices",
    "version": "1.0.0",
    "build_date": "2024-01-01",
    "components": {
      "datahub": true,
      "trading": true
    }
  }
}
```

### Get Health Status
```
GET /system/health
```

Returns overall system health status and individual component health checks.

**Response:**
```json
{
  "success": true,
  "data": {
    "overall_status": "healthy",
    "checks": [
      {
        "component": "datahub",
        "status": "healthy",
        "message": "DataHub service is running",
        "response_time_ms": 5
      }
    ]
  }
}
```

### Get System Metrics
```
GET /system/metrics
```

Returns system performance metrics including CPU, memory, and disk usage.

**Response:**
```json
{
  "success": true,
  "data": {
    "cpu": {
      "usage_percent": 25.5
    },
    "memory": {
      "usage_percent": 45.2,
      "total_bytes": **********,
      "used_bytes": **********
    },
    "disk": {
      "usage_percent": 60.1,
      "total_bytes": 1000000000000,
      "used_bytes": 601000000000
    }
  }
}
```

### Get Services Status
```
GET /system/services
```

Returns status of all registered services.

### Get Configuration
```
GET /system/config
```

Returns current system configuration.

### Update Configuration
```
PUT /system/config
```

Updates system configuration.

## DataHub Endpoints

### Quote Data

#### Get Quote
```
GET /datahub/quote/{symbol}
```

Get the latest quote for a specific symbol.

**Parameters:**
- `symbol` (path) - Stock symbol (e.g., AAPL)

**Response:**
```json
{
  "success": true,
  "data": {
    "symbol": "AAPL",
    "price": 150.25,
    "bid": 150.20,
    "ask": 150.30,
    "volume": 1000000,
    "timestamp": 1640995200000
  }
}
```

#### Get Multiple Quotes
```
GET /datahub/quotes?symbols=AAPL,GOOGL,MSFT
```

Get quotes for multiple symbols.

**Query Parameters:**
- `symbols` - Comma-separated list of symbols

**Response:**
```json
{
  "success": true,
  "data": {
    "quotes": [
      {
        "symbol": "AAPL",
        "price": 150.25,
        "bid": 150.20,
        "ask": 150.30,
        "volume": 1000000,
        "timestamp": 1640995200000
      }
    ],
    "count": 1
  }
}
```

#### Save Quote Data
```
POST /datahub/quote
```

Save quote data to the system.

**Request Body:**
```json
{
  "symbol": "AAPL",
  "price": 150.25,
  "bid": 150.20,
  "ask": 150.30,
  "volume": 1000000
}
```

### Historical Data

#### Get Historical Bars
```
GET /datahub/bars/{symbol}?bar_size=1m&start_time=2024-01-01T00:00:00&end_time=2024-01-01T23:59:59
```

Get historical bar data for a symbol.

**Parameters:**
- `symbol` (path) - Stock symbol
- `bar_size` (query) - Bar size (1s, 5s, 10s, 30s, 1m, 5m, 15m, 30m, 1h, 4h, 1d)
- `start_time` (query, optional) - Start time in ISO format
- `end_time` (query, optional) - End time in ISO format

**Response:**
```json
{
  "success": true,
  "data": {
    "symbol": "AAPL",
    "bar_size": "1m",
    "bars": [
      {
        "timestamp": 1640995200000,
        "open": 150.00,
        "high": 150.50,
        "low": 149.80,
        "close": 150.25,
        "volume": 10000
      }
    ],
    "count": 1
  }
}
```

#### Get Tick Data
```
GET /datahub/ticks/{symbol}?limit=1000
```

Get tick data for a symbol.

**Parameters:**
- `symbol` (path) - Stock symbol
- `limit` (query, optional) - Maximum number of ticks (default: 1000, max: 10000)

### Security Information

#### Get Security Info
```
GET /datahub/security/{symbol}
```

Get detailed information about a security.

#### Search Securities
```
GET /datahub/securities/search?q=apple
```

Search for securities by name or symbol.

#### List Securities
```
GET /datahub/securities
```

Get list of all available securities.

### DataHub Status

#### Get DataHub Status
```
GET /datahub/status
```

Get DataHub service status and statistics.

#### Get DataHub Health
```
GET /datahub/health
```

Get DataHub health check results.

#### Get DataHub Metrics
```
GET /datahub/metrics
```

Get DataHub performance metrics.

## Trading Endpoints

### Strategy Management

#### List Strategies
```
GET /trading/strategies
```

Get list of all trading strategies.

**Response:**
```json
{
  "success": true,
  "data": {
    "strategies": [
      {
        "id": "strategy_001",
        "name": "Momentum Strategy",
        "type": "momentum",
        "status": "running",
        "created_at": 1640995200000,
        "performance": {
          "total_return": 0.15,
          "sharpe_ratio": 1.2,
          "max_drawdown": -0.05
        }
      }
    ],
    "count": 1
  }
}
```

#### Create Strategy
```
POST /trading/strategies
```

Create a new trading strategy.

**Request Body:**
```json
{
  "name": "My Strategy",
  "type": "momentum",
  "parameters": {
    "lookback_period": 20,
    "threshold": 0.02
  },
  "enabled": true
}
```

#### Get Strategy Details
```
GET /trading/strategies/{id}
```

Get detailed information about a specific strategy.

#### Update Strategy
```
PUT /trading/strategies/{id}
```

Update strategy configuration.

#### Delete Strategy
```
DELETE /trading/strategies/{id}
```

Delete a strategy.

#### Start Strategy
```
POST /trading/strategies/{id}/start
```

Start a strategy.

#### Stop Strategy
```
POST /trading/strategies/{id}/stop
```

Stop a strategy.

### Order Management

#### List Orders
```
GET /trading/orders?status=pending&limit=100
```

Get list of orders with optional filtering.

**Query Parameters:**
- `status` (optional) - Filter by order status
- `symbol` (optional) - Filter by symbol
- `strategy_id` (optional) - Filter by strategy
- `limit` (optional) - Maximum number of orders

#### Create Order
```
POST /trading/orders
```

Create a new order.

**Request Body:**
```json
{
  "symbol": "AAPL",
  "side": "buy",
  "quantity": 100,
  "order_type": "market",
  "price": 150.00,
  "strategy_id": "strategy_001"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "order_12345",
    "symbol": "AAPL",
    "side": "buy",
    "quantity": 100,
    "order_type": "market",
    "status": "pending",
    "created_at": 1640995200000
  }
}
```

#### Get Order Details
```
GET /trading/orders/{id}
```

Get detailed information about a specific order.

#### Cancel Order
```
DELETE /trading/orders/{id}
```

Cancel an order.

### Portfolio Management

#### List Portfolios
```
GET /trading/portfolios
```

Get list of all portfolios.

#### Create Portfolio
```
POST /trading/portfolios
```

Create a new portfolio.

#### Get Portfolio Details
```
GET /trading/portfolios/{id}
```

Get detailed portfolio information.

#### Get Portfolio Positions
```
GET /trading/portfolios/{id}/positions
```

Get current positions in a portfolio.

#### Get Portfolio Performance
```
GET /trading/portfolios/{id}/performance
```

Get portfolio performance metrics.

### Risk Management

#### Get Risk Limits
```
GET /trading/risk/limits
```

Get current risk limits.

#### Update Risk Limits
```
PUT /trading/risk/limits
```

Update risk limits.

#### Get Risk Metrics
```
GET /trading/risk/metrics
```

Get current risk metrics.

### Trading Status

#### Get Trading Status
```
GET /trading/status
```

Get trading server status.

#### Get Trading Health
```
GET /trading/health
```

Get trading server health.

#### Get Trading Metrics
```
GET /trading/metrics
```

Get trading server metrics.

## Error Handling

### Common Error Codes

- `VALIDATION_ERROR` - Invalid request parameters
- `NOT_FOUND` - Resource not found
- `SERVICE_ERROR` - Internal service error
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `INSUFFICIENT_FUNDS` - Not enough funds for operation
- `RISK_LIMIT_EXCEEDED` - Operation would exceed risk limits

### Error Response Example

```json
{
  "success": false,
  "error": {
    "message": "Validation error",
    "details": "Field 'symbol': Invalid symbol format",
    "code": 400
  },
  "timestamp": 1640995200000
}
```

## Rate Limiting

API requests are subject to rate limiting:
- Default: 1000 requests per minute per IP
- Burst: 100 requests
- Headers included in response:
  - `X-RateLimit-Limit`
  - `X-RateLimit-Remaining`
  - `X-RateLimit-Reset`

## Authentication

Currently, authentication is optional and can be enabled via configuration:

```json
{
  "api": {
    "authentication": {
      "enabled": true,
      "type": "api_key",
      "api_key": "your-api-key-here"
    }
  }
}
```

When enabled, include the API key in the request header:
```
X-API-Key: your-api-key-here
```
