/**
 * @file RestApiServer.cpp
 * @brief Implementation of REST API server
 * <AUTHOR> Team
 * @date 2024
 */

#include "quantservices/RestApiServer.h"
#include <spdlog/spdlog.h>
#include <boost/beast/core.hpp>
#include <boost/beast/http.hpp>
#include <boost/beast/version.hpp>
#include <boost/asio/dispatch.hpp>
#include <boost/asio/strand.hpp>
#include <boost/config.hpp>
#include <algorithm>
#include <cstdlib>
#include <functional>
#include <iostream>
#include <memory>
#include <string>
#include <thread>
#include <vector>
#include <regex>
#include <random>
#include <sstream>
#include <iomanip>

namespace QuantServices {

namespace beast = boost::beast;
namespace http = beast::http;
namespace net = boost::asio;
using tcp = boost::asio::ip::tcp;

// Utility functions implementation

std::string to_string(HttpMethod method) {
    switch (method) {
        case HttpMethod::GET: return "GET";
        case HttpMethod::POST: return "POST";
        case HttpMethod::PUT: return "PUT";
        case HttpMethod::DELETE: return "DELETE";
        case HttpMethod::PATCH: return "PATCH";
        case HttpMethod::OPTIONS: return "OPTIONS";
        default: return "UNKNOWN";
    }
}

HttpMethod parse_http_method(const std::string& method) {
    if (method == "GET") return HttpMethod::GET;
    if (method == "POST") return HttpMethod::POST;
    if (method == "PUT") return HttpMethod::PUT;
    if (method == "DELETE") return HttpMethod::DELETE;
    if (method == "PATCH") return HttpMethod::PATCH;
    if (method == "OPTIONS") return HttpMethod::OPTIONS;
    return HttpMethod::GET; // Default
}

// ApiResponse implementation

ApiResponse ApiResponse::success(const Json& data) {
    ApiResponse response;
    response.status = HttpStatus::OK;
    response.body = Json{
        {"success", true},
        {"data", data},
        {"timestamp", std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count()}
    };
    return response;
}

ApiResponse ApiResponse::error(HttpStatus status, const std::string& message, const std::string& details) {
    ApiResponse response;
    response.status = status;
    response.body = Json{
        {"success", false},
        {"error", {
            {"message", message},
            {"details", details},
            {"code", static_cast<int>(status)}
        }},
        {"timestamp", std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count()}
    };
    return response;
}

ApiResponse ApiResponse::not_found(const std::string& resource) {
    return error(HttpStatus::NotFound, resource + " not found");
}

ApiResponse ApiResponse::bad_request(const std::string& message) {
    return error(HttpStatus::BadRequest, message);
}

ApiResponse ApiResponse::internal_error(const std::string& message) {
    return error(HttpStatus::InternalServerError, message);
}

// HTTP session class for handling individual connections
class HttpSession : public std::enable_shared_from_this<HttpSession> {
public:
    HttpSession(tcp::socket&& socket, RestApiServer* server)
        : stream_(std::move(socket))
        , server_(server) {
    }

    void run() {
        net::dispatch(stream_.get_executor(),
                     beast::bind_front_handler(&HttpSession::do_read, shared_from_this()));
    }

private:
    beast::tcp_stream stream_;
    beast::flat_buffer buffer_;
    HttpRequest req_;
    std::shared_ptr<void> res_;
    RestApiServer* server_;

    void do_read() {
        req_ = {};
        stream_.expires_after(std::chrono::seconds(30));

        http::async_read(stream_, buffer_, req_,
            beast::bind_front_handler(&HttpSession::on_read, shared_from_this()));
    }

    void on_read(beast::error_code ec, std::size_t bytes_transferred) {
        boost::ignore_unused(bytes_transferred);

        if (ec == http::error::end_of_stream) {
            return do_close();
        }

        if (ec) {
            return; // Connection error
        }

        handle_request();
    }

    void handle_request() {
        // Get client IP
        std::string client_ip = stream_.socket().remote_endpoint().address().to_string();
        
        // Process request through server
        auto api_response = server_->process_request(req_, client_ip);
        
        // Create HTTP response
        auto http_response = server_->create_http_response(api_response, "req-" + std::to_string(rand()));
        
        // Send response
        auto sp = std::make_shared<HttpResponse>(std::move(http_response));
        res_ = sp;

        http::async_write(stream_, *sp,
            beast::bind_front_handler(&HttpSession::on_write, shared_from_this(), sp->need_eof()));
    }

    void on_write(bool close, beast::error_code ec, std::size_t bytes_transferred) {
        boost::ignore_unused(bytes_transferred);

        if (ec) {
            return; // Write error
        }

        if (close) {
            return do_close();
        }

        res_ = nullptr;
        do_read();
    }

    void do_close() {
        beast::error_code ec;
        stream_.socket().shutdown(tcp::socket::shutdown_send, ec);
    }
};

// RestApiServer implementation

RestApiServer::RestApiServer(const ServerConfig& config)
    : config_(config)
    , logger_(spdlog::get("quantservices") ? spdlog::get("quantservices") : spdlog::default_logger()) {
    
    metrics_.start_time = std::chrono::system_clock::now();
    logger_->info("RestApiServer created with config: {}:{}", config_.host, config_.port);
}

RestApiServer::~RestApiServer() {
    if (running_.load()) {
        auto stop_future = stop();
        stop_future.wait();
    }
}

std::future<bool> RestApiServer::start() {
    return std::async(std::launch::async, [this]() -> bool {
        try {
            if (running_.load()) {
                logger_->warn("Server is already running");
                return true;
            }

            logger_->info("Starting REST API server on {}:{}", config_.host, config_.port);

            // Create acceptor
            auto const address = net::ip::make_address(config_.host);
            acceptor_ = std::make_unique<tcp::acceptor>(io_context_, tcp::endpoint{address, config_.port});

            running_.store(true);

            // Start worker threads
            worker_threads_.reserve(config_.thread_count);
            for (size_t i = 0; i < config_.thread_count; ++i) {
                worker_threads_.emplace_back(&RestApiServer::run_worker, this);
            }

            // Start accepting connections
            handle_accept();

            logger_->info("REST API server started successfully");
            return true;

        } catch (const std::exception& e) {
            logger_->error("Failed to start REST API server: {}", e.what());
            running_.store(false);
            return false;
        }
    });
}

std::future<void> RestApiServer::stop() {
    return std::async(std::launch::async, [this]() {
        try {
            if (!running_.load()) {
                return;
            }

            logger_->info("Stopping REST API server...");
            running_.store(false);

            // Close acceptor
            if (acceptor_) {
                acceptor_->close();
            }

            // Stop io_context
            io_context_.stop();

            // Join worker threads
            for (auto& thread : worker_threads_) {
                if (thread.joinable()) {
                    thread.join();
                }
            }
            worker_threads_.clear();

            logger_->info("REST API server stopped");

        } catch (const std::exception& e) {
            logger_->error("Exception during server shutdown: {}", e.what());
        }
    });
}

bool RestApiServer::is_running() const {
    return running_.load();
}

void RestApiServer::register_route(HttpMethod method, const std::string& pattern, 
                                  RouteHandler handler, const std::string& description) {
    std::unique_lock lock(routes_mutex_);
    
    Route route;
    route.method = method;
    route.pattern = pattern;
    route.handler = std::move(handler);
    route.description = description;
    
    routes_.push_back(std::move(route));
    
    logger_->debug("Registered route: {} {}", to_string(method), pattern);
}

void RestApiServer::register_middleware(Middleware middleware) {
    std::unique_lock lock(routes_mutex_);
    global_middlewares_.push_back(std::move(middleware));
    logger_->debug("Registered global middleware");
}

void RestApiServer::register_route_with_middlewares(HttpMethod method, const std::string& pattern,
                                                   RouteHandler handler, 
                                                   const std::vector<Middleware>& middlewares,
                                                   const std::string& description) {
    std::unique_lock lock(routes_mutex_);
    
    Route route;
    route.method = method;
    route.pattern = pattern;
    route.handler = std::move(handler);
    route.middlewares = middlewares;
    route.description = description;
    
    routes_.push_back(std::move(route));
    
    logger_->debug("Registered route with middlewares: {} {}", to_string(method), pattern);
}

const ServerConfig& RestApiServer::get_config() const {
    return config_;
}

Json RestApiServer::get_metrics() const {
    std::lock_guard lock(metrics_mutex_);
    
    Json metrics;
    metrics["total_requests"] = metrics_.total_requests.load();
    metrics["successful_requests"] = metrics_.successful_requests.load();
    metrics["failed_requests"] = metrics_.failed_requests.load();
    metrics["bytes_sent"] = metrics_.bytes_sent.load();
    metrics["bytes_received"] = metrics_.bytes_received.load();
    metrics["uptime_seconds"] = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now() - metrics_.start_time).count();
    metrics["running"] = running_.load();
    
    return metrics;
}

std::vector<Route> RestApiServer::get_routes() const {
    std::shared_lock lock(routes_mutex_);
    return routes_;
}

// Private methods implementation

void RestApiServer::run_worker() {
    try {
        io_context_.run();
    } catch (const std::exception& e) {
        logger_->error("Exception in worker thread: {}", e.what());
    }
}

void RestApiServer::handle_accept() {
    if (!running_.load()) {
        return;
    }

    acceptor_->async_accept(
        net::make_strand(io_context_),
        [this](beast::error_code ec, tcp::socket socket) {
            if (!ec) {
                std::make_shared<HttpSession>(std::move(socket), this)->run();
            } else if (ec != net::error::operation_aborted) {
                logger_->error("Accept error: {}", ec.message());
            }

            // Continue accepting connections
            handle_accept();
        });
}

ApiResponse RestApiServer::process_request(const HttpRequest& req, const std::string& client_ip) {
    try {
        // Create request context
        auto context = create_request_context(req, client_ip);

        // Update metrics
        metrics_.total_requests.fetch_add(1);
        metrics_.bytes_received.fetch_add(req.body().size());

        // Handle CORS preflight
        if (context.request.method() == http::verb::options) {
            ApiResponse response = ApiResponse::success();
            apply_cors_headers(response);
            return response;
        }

        // Apply global middlewares
        ApiResponse middleware_response;
        for (const auto& middleware : global_middlewares_) {
            if (!middleware(const_cast<RequestContext&>(context), middleware_response)) {
                return middleware_response;
            }
        }

        // Find matching route
        std::shared_lock lock(routes_mutex_);
        for (const auto& route : routes_) {
            std::unordered_map<std::string, std::string> path_params;
            if (match_route(std::string(context.request.method_string()),
                           context.path, route, path_params)) {

                // Update context with path parameters
                const_cast<RequestContext&>(context).path_params = path_params;

                // Apply route-specific middlewares
                for (const auto& middleware : route.middlewares) {
                    if (!middleware(const_cast<RequestContext&>(context), middleware_response)) {
                        return middleware_response;
                    }
                }

                // Call route handler
                auto response = route.handler(context);

                // Apply CORS headers
                if (config_.enable_cors) {
                    apply_cors_headers(response);
                }

                // Update metrics
                if (static_cast<int>(response.status) >= 200 && static_cast<int>(response.status) < 400) {
                    metrics_.successful_requests.fetch_add(1);
                } else {
                    metrics_.failed_requests.fetch_add(1);
                }

                update_metrics(context, response);
                return response;
            }
        }

        // No route found
        metrics_.failed_requests.fetch_add(1);
        return ApiResponse::not_found("Endpoint");

    } catch (const std::exception& e) {
        logger_->error("Exception processing request: {}", e.what());
        metrics_.failed_requests.fetch_add(1);
        return ApiResponse::internal_error("Internal server error");
    }
}

RequestContext RestApiServer::create_request_context(const HttpRequest& req, const std::string& client_ip) {
    RequestContext context;
    context.request = req;
    context.client_ip = client_ip;
    context.start_time = std::chrono::system_clock::now();
    context.request_id = generate_request_id();

    // Parse URL
    std::string target = std::string(req.target());
    auto query_pos = target.find('?');
    if (query_pos != std::string::npos) {
        context.path = target.substr(0, query_pos);
        context.query_string = target.substr(query_pos + 1);
        context.query_params = parse_query_string(context.query_string);
    } else {
        context.path = target;
    }

    // Remove API prefix if present
    if (context.path.starts_with(config_.api_prefix)) {
        context.path = context.path.substr(config_.api_prefix.length());
    }

    // Parse headers
    for (const auto& header : req) {
        context.headers[std::string(header.name_string())] = std::string(header.value());
    }

    // Parse JSON body if present
    if (req.body().size() > 0) {
        try {
            context.body_json = Json::parse(req.body());
        } catch (const std::exception& e) {
            logger_->debug("Failed to parse JSON body: {}", e.what());
            // Not all requests have JSON body, this is okay
        }
    }

    return context;
}

HttpResponse RestApiServer::create_http_response(const ApiResponse& api_response, const std::string& request_id) {
    HttpResponse response;
    response.version(11); // HTTP/1.1
    response.result(static_cast<http::status>(api_response.status));
    response.set(http::field::server, "QuantServices/1.0");
    response.set(http::field::content_type, "application/json");
    response.set("X-Request-ID", request_id);

    // Add custom headers
    for (const auto& [key, value] : api_response.headers) {
        response.set(key, value);
    }

    // Set body
    std::string body_str = api_response.body.dump();
    response.body() = body_str;
    response.prepare_payload();

    // Update metrics
    metrics_.bytes_sent.fetch_add(body_str.size());

    return response;
}

bool RestApiServer::match_route(const std::string& method, const std::string& path,
                               const Route& route, std::unordered_map<std::string, std::string>& path_params) {
    // Check method
    if (to_string(route.method) != method) {
        return false;
    }

    // Simple pattern matching - could be enhanced with regex
    if (route.pattern == path) {
        return true;
    }

    // Handle path parameters (simple implementation)
    // Pattern: /api/{id} matches /api/123
    std::regex pattern_regex(route.pattern);
    std::regex param_regex(R"(\{([^}]+)\})");

    std::string regex_pattern = route.pattern;
    std::sregex_iterator iter(route.pattern.begin(), route.pattern.end(), param_regex);
    std::sregex_iterator end;

    std::vector<std::string> param_names;
    for (; iter != end; ++iter) {
        param_names.push_back((*iter)[1].str());
        regex_pattern = std::regex_replace(regex_pattern, std::regex(R"(\{[^}]+\})"), "([^/]+)", std::regex_constants::format_first_only);
    }

    std::regex final_regex("^" + regex_pattern + "$");
    std::smatch matches;

    if (std::regex_match(path, matches, final_regex)) {
        for (size_t i = 0; i < param_names.size() && i + 1 < matches.size(); ++i) {
            path_params[param_names[i]] = matches[i + 1].str();
        }
        return true;
    }

    return false;
}

void RestApiServer::apply_cors_headers(HttpResponse& response) {
    if (!config_.enable_cors) {
        return;
    }

    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    response.set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
    response.set("Access-Control-Max-Age", "3600");
}

void RestApiServer::apply_cors_headers(ApiResponse& response) {
    if (!config_.enable_cors) {
        return;
    }

    response.headers["Access-Control-Allow-Origin"] = "*"; // Should be configurable
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS";
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With";
    response.headers["Access-Control-Max-Age"] = "3600";
}

void RestApiServer::update_metrics(const RequestContext& context, const ApiResponse& response) {
    // Calculate response time
    auto response_time = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now() - context.start_time);

    // Log request
    if (config_.enable_logging) {
        logger_->info("{} {} {} {} {}ms",
                     context.client_ip,
                     std::string(context.request.method_string()),
                     context.path,
                     static_cast<int>(response.status),
                     response_time.count());
    }
}

std::string RestApiServer::generate_request_id() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(0, 15);

    std::stringstream ss;
    ss << std::hex;
    for (int i = 0; i < 8; ++i) {
        ss << dis(gen);
    }
    return ss.str();
}

std::unordered_map<std::string, std::string> RestApiServer::parse_query_string(const std::string& query) {
    std::unordered_map<std::string, std::string> params;

    std::stringstream ss(query);
    std::string pair;

    while (std::getline(ss, pair, '&')) {
        auto eq_pos = pair.find('=');
        if (eq_pos != std::string::npos) {
            std::string key = pair.substr(0, eq_pos);
            std::string value = pair.substr(eq_pos + 1);
            params[key] = value;
        }
    }

    return params;
}

// Middleware implementations

namespace Middleware {

Middleware cors(const std::vector<std::string>& allowed_origins) {
    return [allowed_origins](RequestContext& context, ApiResponse& response) -> bool {
        // Add CORS headers
        response.headers["Access-Control-Allow-Origin"] = allowed_origins.empty() ? "*" : allowed_origins[0];
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS";
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With";
        response.headers["Access-Control-Max-Age"] = "3600";

        // Handle preflight requests
        if (context.request.method() == http::verb::options) {
            response = ApiResponse::success();
            response.status = HttpStatus::NoContent;
            return false; // Stop processing, return preflight response
        }

        return true; // Continue processing
    };
}

Middleware request_logging() {
    return [](RequestContext& context, ApiResponse& response) -> bool {
        auto logger = spdlog::get("quantservices");
        if (logger) {
            logger->info("Request: {} {} from {}",
                        std::string(context.request.method_string()),
                        context.path,
                        context.client_ip);
        }
        return true;
    };
}

Middleware request_size_limit(size_t max_size) {
    return [max_size](RequestContext& context, ApiResponse& response) -> bool {
        if (context.request.body().size() > max_size) {
            response = ApiResponse::error(HttpStatus::BadRequest,
                                        "Request body too large",
                                        "Maximum size: " + std::to_string(max_size) + " bytes");
            return false;
        }
        return true;
    };
}

Middleware api_key_auth(const std::string& api_key) {
    return [api_key](RequestContext& context, ApiResponse& response) -> bool {
        if (api_key.empty()) {
            return true; // No authentication required
        }

        auto it = context.headers.find("X-API-Key");
        if (it == context.headers.end() || it->second != api_key) {
            response = ApiResponse::error(HttpStatus::Unauthorized, "Invalid or missing API key");
            return false;
        }
        return true;
    };
}

Middleware rate_limit(size_t requests_per_minute) {
    // Simple rate limiting implementation
    static std::unordered_map<std::string, std::vector<std::chrono::system_clock::time_point>> client_requests;
    static std::mutex rate_limit_mutex;

    return [requests_per_minute](RequestContext& context, ApiResponse& response) -> bool {
        std::lock_guard lock(rate_limit_mutex);

        auto now = std::chrono::system_clock::now();
        auto minute_ago = now - std::chrono::minutes(1);

        auto& requests = client_requests[context.client_ip];

        // Remove old requests
        requests.erase(
            std::remove_if(requests.begin(), requests.end(),
                          [minute_ago](const auto& time) { return time < minute_ago; }),
            requests.end());

        // Check rate limit
        if (requests.size() >= requests_per_minute) {
            response = ApiResponse::error(HttpStatus::BadRequest, "Rate limit exceeded");
            return false;
        }

        // Add current request
        requests.push_back(now);
        return true;
    };
}

Middleware request_timeout(std::chrono::seconds timeout) {
    return [timeout](RequestContext& context, ApiResponse& response) -> bool {
        auto elapsed = std::chrono::system_clock::now() - context.start_time;
        if (elapsed > timeout) {
            response = ApiResponse::error(HttpStatus::BadRequest, "Request timeout");
            return false;
        }
        return true;
    };
}

} // namespace Middleware

} // namespace QuantServices
