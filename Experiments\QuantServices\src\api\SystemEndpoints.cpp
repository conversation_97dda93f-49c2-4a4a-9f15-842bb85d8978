/**
 * @file SystemEndpoints.cpp
 * @brief Implementation of System REST API endpoints
 * <AUTHOR> Team
 * @date 2024
 */

#include "quantservices/api/SystemEndpoints.h"
#include <spdlog/spdlog.h>
#include <chrono>
#include <fstream>

namespace QuantServices {

SystemEndpoints::SystemEndpoints(std::shared_ptr<ServiceManager> service_manager,
                                 std::shared_ptr<HealthMonitor> health_monitor,
                                 std::shared_ptr<ConfigManager> config_manager)
    : service_manager_(service_manager)
    , health_monitor_(health_monitor)
    , config_manager_(config_manager) {
}

void SystemEndpoints::register_endpoints(RestApiServer& server) {
    // System information endpoints
    server.register_route(HttpMethod::GET, "/system/info", 
        [this](const RequestContext& context) { return get_system_info(context); },
        "Get system information");
    
    server.register_route(HttpMethod::GET, "/system/version", 
        [this](const RequestContext& context) { return get_version_info(context); },
        "Get version information");
    
    server.register_route(HttpMethod::GET, "/system/build", 
        [this](const RequestContext& context) { return get_build_info(context); },
        "Get build information");
    
    // Health and monitoring endpoints
    server.register_route(HttpMethod::GET, "/system/health", 
        [this](const RequestContext& context) { return get_health_status(context); },
        "Get overall system health status");
    
    server.register_route(HttpMethod::GET, "/system/health/{component}", 
        [this](const RequestContext& context) { return get_health_check(context); },
        "Get health check for specific component");
    
    server.register_route(HttpMethod::GET, "/system/metrics", 
        [this](const RequestContext& context) { return get_system_metrics(context); },
        "Get system performance metrics");
    
    server.register_route(HttpMethod::GET, "/system/metrics/history", 
        [this](const RequestContext& context) { return get_historical_metrics(context); },
        "Get historical system metrics");
    
    server.register_route(HttpMethod::GET, "/system/performance", 
        [this](const RequestContext& context) { return get_performance_metrics(context); },
        "Get performance metrics");
    
    // Service management endpoints
    server.register_route(HttpMethod::GET, "/system/services", 
        [this](const RequestContext& context) { return get_services_status(context); },
        "Get status of all services");
    
    server.register_route(HttpMethod::GET, "/system/services/{name}", 
        [this](const RequestContext& context) { return get_service_status(context); },
        "Get status of specific service");
    
    server.register_route(HttpMethod::POST, "/system/services/{name}/start", 
        [this](const RequestContext& context) { return start_service(context); },
        "Start a service");
    
    server.register_route(HttpMethod::POST, "/system/services/{name}/stop", 
        [this](const RequestContext& context) { return stop_service(context); },
        "Stop a service");
    
    server.register_route(HttpMethod::POST, "/system/services/{name}/restart", 
        [this](const RequestContext& context) { return restart_service(context); },
        "Restart a service");
    
    // Configuration management endpoints
    server.register_route(HttpMethod::GET, "/system/config", 
        [this](const RequestContext& context) { return get_configuration(context); },
        "Get system configuration");
    
    server.register_route(HttpMethod::PUT, "/system/config", 
        [this](const RequestContext& context) { return update_configuration(context); },
        "Update system configuration");
    
    server.register_route(HttpMethod::GET, "/system/config/{section}", 
        [this](const RequestContext& context) { return get_config_section(context); },
        "Get configuration section");
    
    server.register_route(HttpMethod::PUT, "/system/config/{section}", 
        [this](const RequestContext& context) { return update_config_section(context); },
        "Update configuration section");
    
    server.register_route(HttpMethod::POST, "/system/config/validate", 
        [this](const RequestContext& context) { return validate_configuration(context); },
        "Validate configuration");
    
    server.register_route(HttpMethod::POST, "/system/config/reload", 
        [this](const RequestContext& context) { return reload_configuration(context); },
        "Reload configuration from file");
    
    // Logging endpoints
    server.register_route(HttpMethod::GET, "/system/log/level", 
        [this](const RequestContext& context) { return get_log_level(context); },
        "Get current log level");
    
    server.register_route(HttpMethod::PUT, "/system/log/level", 
        [this](const RequestContext& context) { return set_log_level(context); },
        "Set log level");
    
    server.register_route(HttpMethod::GET, "/system/logs", 
        [this](const RequestContext& context) { return get_recent_logs(context); },
        "Get recent log entries");
    
    // API documentation endpoints
    server.register_route(HttpMethod::GET, "/system/api/docs", 
        [this](const RequestContext& context) { return get_api_documentation(context); },
        "Get API documentation");
    
    server.register_route(HttpMethod::GET, "/system/api/openapi", 
        [this](const RequestContext& context) { return get_openapi_spec(context); },
        "Get OpenAPI specification");
    
    server.register_route(HttpMethod::GET, "/system/api/endpoints", 
        [this](const RequestContext& context) { return get_endpoint_list(context); },
        "Get list of all API endpoints");
    
    // Server management endpoints
    server.register_route(HttpMethod::POST, "/system/shutdown", 
        [this](const RequestContext& context) { return shutdown_server(context); },
        "Shutdown the server");
    
    server.register_route(HttpMethod::POST, "/system/restart", 
        [this](const RequestContext& context) { return restart_server(context); },
        "Restart the server");
    
    server.register_route(HttpMethod::GET, "/system/stats", 
        [this](const RequestContext& context) { return get_server_stats(context); },
        "Get server statistics");
    
    // Diagnostic endpoints
    server.register_route(HttpMethod::POST, "/system/diagnostics", 
        [this](const RequestContext& context) { return run_diagnostics(context); },
        "Run system diagnostics");
    
    server.register_route(HttpMethod::GET, "/system/diagnostics/report", 
        [this](const RequestContext& context) { return get_diagnostic_report(context); },
        "Get diagnostic report");
    
    server.register_route(HttpMethod::POST, "/system/test/connectivity", 
        [this](const RequestContext& context) { return test_connectivity(context); },
        "Test system connectivity");
}

// System information endpoint implementations

ApiResponse SystemEndpoints::get_system_info(const RequestContext& context) {
    try {
        Json system_info = create_system_info();
        return ApiResponse::success(system_info);
        
    } catch (const std::exception& e) {
        return ApiResponse::internal_error("Failed to get system info: " + std::string(e.what()));
    }
}

ApiResponse SystemEndpoints::get_version_info(const RequestContext& context) {
    try {
        Json version_info = create_version_info();
        return ApiResponse::success(version_info);
        
    } catch (const std::exception& e) {
        return ApiResponse::internal_error("Failed to get version info: " + std::string(e.what()));
    }
}

ApiResponse SystemEndpoints::get_build_info(const RequestContext& context) {
    try {
        Json build_info = create_build_info();
        return ApiResponse::success(build_info);
        
    } catch (const std::exception& e) {
        return ApiResponse::internal_error("Failed to get build info: " + std::string(e.what()));
    }
}

// Health and monitoring endpoint implementations

ApiResponse SystemEndpoints::get_health_status(const RequestContext& context) {
    try {
        if (!health_monitor_) {
            return ApiResponse::internal_error("Health monitor not available");
        }
        
        auto health_summary = health_monitor_->get_health_summary();
        return ApiResponse::success(health_summary);
        
    } catch (const std::exception& e) {
        return ApiResponse::internal_error("Failed to get health status: " + std::string(e.what()));
    }
}

ApiResponse SystemEndpoints::get_health_check(const RequestContext& context) {
    try {
        std::string component = extract_service_name(context);
        if (component.empty()) {
            return ApiResponse::bad_request("Component name is required");
        }
        
        if (!health_monitor_) {
            return ApiResponse::internal_error("Health monitor not available");
        }
        
        auto result = health_monitor_->run_health_check(component);
        return ApiResponse::success(result.to_json());
        
    } catch (const std::exception& e) {
        return ApiResponse::internal_error("Failed to run health check: " + std::string(e.what()));
    }
}

ApiResponse SystemEndpoints::get_system_metrics(const RequestContext& context) {
    try {
        if (!health_monitor_) {
            return ApiResponse::internal_error("Health monitor not available");
        }
        
        auto metrics = health_monitor_->get_system_metrics();
        return ApiResponse::success(metrics.to_json());
        
    } catch (const std::exception& e) {
        return ApiResponse::internal_error("Failed to get system metrics: " + std::string(e.what()));
    }
}

ApiResponse SystemEndpoints::get_historical_metrics(const RequestContext& context) {
    try {
        if (!health_monitor_) {
            return ApiResponse::internal_error("Health monitor not available");
        }
        
        // Parse duration parameter
        int duration_minutes = 60; // Default 1 hour
        auto duration_it = context.query_params.find("duration");
        if (duration_it != context.query_params.end()) {
            try {
                duration_minutes = std::stoi(duration_it->second);
                if (duration_minutes <= 0 || duration_minutes > 1440) { // Max 24 hours
                    return ApiResponse::bad_request("Duration must be between 1 and 1440 minutes");
                }
            } catch (const std::exception&) {
                return ApiResponse::bad_request("Invalid duration parameter");
            }
        }
        
        auto historical_metrics = health_monitor_->get_historical_metrics(
            std::chrono::minutes(duration_minutes));
        
        Json response_data = {
            {"duration_minutes", duration_minutes},
            {"metrics", Json::array()},
            {"count", historical_metrics.size()}
        };
        
        for (const auto& metrics : historical_metrics) {
            response_data["metrics"].push_back(metrics.to_json());
        }
        
        return ApiResponse::success(response_data);
        
    } catch (const std::exception& e) {
        return ApiResponse::internal_error("Failed to get historical metrics: " + std::string(e.what()));
    }
}

ApiResponse SystemEndpoints::get_performance_metrics(const RequestContext& context) {
    try {
        Json performance_data;
        
        // Get service performance metrics
        if (service_manager_) {
            performance_data["services"] = service_manager_->get_performance_metrics();
        }
        
        // Get health monitor metrics
        if (health_monitor_) {
            auto system_metrics = health_monitor_->get_system_metrics();
            performance_data["system"] = system_metrics.to_json();
        }
        
        performance_data["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        return ApiResponse::success(performance_data);
        
    } catch (const std::exception& e) {
        return ApiResponse::internal_error("Failed to get performance metrics: " + std::string(e.what()));
    }
}

// Service management endpoint implementations

ApiResponse SystemEndpoints::get_services_status(const RequestContext& context) {
    try {
        if (!service_manager_) {
            return ApiResponse::internal_error("Service manager not available");
        }
        
        auto services_status = service_manager_->get_all_services_status();
        
        Json response_data = {
            {"services", Json::object()},
            {"count", services_status.size()},
            {"all_running", service_manager_->are_all_services_running()}
        };
        
        for (const auto& [name, info] : services_status) {
            response_data["services"][name] = info.to_json();
        }
        
        return ApiResponse::success(response_data);
        
    } catch (const std::exception& e) {
        return ApiResponse::internal_error("Failed to get services status: " + std::string(e.what()));
    }
}

ApiResponse SystemEndpoints::get_service_status(const RequestContext& context) {
    try {
        std::string service_name = extract_service_name(context);
        if (service_name.empty()) {
            return ApiResponse::bad_request("Service name is required");
        }
        
        if (!validate_service_name(service_name)) {
            return create_service_not_found_error(service_name);
        }
        
        if (!service_manager_) {
            return ApiResponse::internal_error("Service manager not available");
        }
        
        auto status = service_manager_->get_service_status(service_name);
        auto all_services = service_manager_->get_all_services_status();
        
        auto it = all_services.find(service_name);
        if (it != all_services.end()) {
            return ApiResponse::success(it->second.to_json());
        } else {
            return create_service_not_found_error(service_name);
        }
        
    } catch (const std::exception& e) {
        return ApiResponse::internal_error("Failed to get service status: " + std::string(e.what()));
    }
}

// Utility method implementations

bool SystemEndpoints::validate_service_name(const std::string& service_name) const {
    return service_name == "datahub" || service_name == "trading";
}

bool SystemEndpoints::validate_log_level(const std::string& log_level) const {
    return log_level == "trace" || log_level == "debug" || log_level == "info" ||
           log_level == "warn" || log_level == "error" || log_level == "critical";
}

bool SystemEndpoints::validate_config_section(const std::string& section) const {
    return section == "server" || section == "services" || section == "logging" ||
           section == "health_monitoring" || section == "api";
}

Json SystemEndpoints::create_system_info() const {
    Json info = {
        {"service", "QuantServices"},
        {"version", VERSION},
        {"status", "running"},
        {"uptime_seconds", 0}, // Would calculate actual uptime
        {"components", {
            {"datahub", true},
            {"trading", true},
            {"health_monitor", health_monitor_ != nullptr},
            {"config_manager", config_manager_ != nullptr}
        }},
        {"platform", {
#ifdef _WIN32
            {"os", "Windows"},
#elif __linux__
            {"os", "Linux"},
#elif __APPLE__
            {"os", "macOS"},
#else
            {"os", "Unknown"},
#endif
            {"architecture", sizeof(void*) == 8 ? "x64" : "x86"}
        }},
        {"timestamp", std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count()}
    };
    return info;
}

Json SystemEndpoints::create_version_info() const {
    Json version = {
        {"version", VERSION},
        {"version_major", VERSION_MAJOR},
        {"version_minor", VERSION_MINOR},
        {"version_patch", VERSION_PATCH},
        {"build_date", __DATE__},
        {"build_time", __TIME__},
        {"compiler", {
#ifdef _MSC_VER
            {"name", "MSVC"},
            {"version", _MSC_VER}
#elif __GNUC__
            {"name", "GCC"},
            {"version", __GNUC__ * 10000 + __GNUC_MINOR__ * 100 + __GNUC_PATCHLEVEL__}
#elif __clang__
            {"name", "Clang"},
            {"version", __clang_major__ * 10000 + __clang_minor__ * 100 + __clang_patchlevel__}
#else
            {"name", "Unknown"},
            {"version", 0}
#endif
        }},
        {"cpp_standard", __cplusplus}
    };
    return version;
}

Json SystemEndpoints::create_build_info() const {
    Json build = {
        {"build_type",
#ifdef DEBUG
            "Debug"
#else
            "Release"
#endif
        },
        {"build_date", __DATE__},
        {"build_time", __TIME__},
        {"git_commit", "unknown"}, // Would be filled by build system
        {"git_branch", "unknown"}, // Would be filled by build system
        {"dependencies", {
            {"boost", "1.75+"},
            {"nlohmann_json", "3.11+"},
            {"spdlog", "1.12+"},
            {"fmt", "10.1+"}
        }}
    };
    return build;
}

Json SystemEndpoints::create_server_stats() const {
    Json stats = {
        {"start_time", std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count()},
        {"uptime_seconds", 0}, // Would calculate actual uptime
        {"total_requests", 0}, // Would get from API server
        {"active_connections", 0}, // Would get from API server
        {"memory_usage_bytes", 0}, // Would get from system metrics
        {"cpu_usage_percent", 0.0} // Would get from system metrics
    };
    return stats;
}

std::string SystemEndpoints::extract_service_name(const RequestContext& context) const {
    auto it = context.path_params.find("name");
    if (it != context.path_params.end()) {
        return it->second;
    }

    // Try "component" parameter for health checks
    it = context.path_params.find("component");
    if (it != context.path_params.end()) {
        return it->second;
    }

    return "";
}

std::string SystemEndpoints::extract_config_section(const RequestContext& context) const {
    auto it = context.path_params.find("section");
    return it != context.path_params.end() ? it->second : "";
}

// Error handling methods

ApiResponse SystemEndpoints::create_service_not_found_error(const std::string& service_name) const {
    return ApiResponse::not_found("Service '" + service_name + "'");
}

ApiResponse SystemEndpoints::create_config_validation_error(const std::string& message) const {
    return ApiResponse::error(HttpStatus::BadRequest, "Configuration validation error", message);
}

ApiResponse SystemEndpoints::create_permission_denied_error() const {
    return ApiResponse::error(HttpStatus::Forbidden, "Permission denied",
                             "Insufficient privileges for this operation");
}

// Placeholder implementations for remaining endpoints

#define IMPLEMENT_PLACEHOLDER(method_name) \
ApiResponse SystemEndpoints::method_name(const RequestContext& context) { \
    return ApiResponse::error(HttpStatus::NotImplemented, "Endpoint not implemented yet", #method_name); \
}

IMPLEMENT_PLACEHOLDER(start_service)
IMPLEMENT_PLACEHOLDER(stop_service)
IMPLEMENT_PLACEHOLDER(restart_service)
IMPLEMENT_PLACEHOLDER(get_configuration)
IMPLEMENT_PLACEHOLDER(update_configuration)
IMPLEMENT_PLACEHOLDER(get_config_section)
IMPLEMENT_PLACEHOLDER(update_config_section)
IMPLEMENT_PLACEHOLDER(validate_configuration)
IMPLEMENT_PLACEHOLDER(reload_configuration)
IMPLEMENT_PLACEHOLDER(get_log_level)
IMPLEMENT_PLACEHOLDER(set_log_level)
IMPLEMENT_PLACEHOLDER(get_recent_logs)
IMPLEMENT_PLACEHOLDER(get_api_documentation)
IMPLEMENT_PLACEHOLDER(get_openapi_spec)
IMPLEMENT_PLACEHOLDER(get_endpoint_list)
IMPLEMENT_PLACEHOLDER(shutdown_server)
IMPLEMENT_PLACEHOLDER(restart_server)
IMPLEMENT_PLACEHOLDER(get_server_stats)
IMPLEMENT_PLACEHOLDER(run_diagnostics)
IMPLEMENT_PLACEHOLDER(get_diagnostic_report)
IMPLEMENT_PLACEHOLDER(test_connectivity)

#undef IMPLEMENT_PLACEHOLDER

// Helper namespace implementation

namespace SystemEndpointHelper {

void register_all_endpoints(RestApiServer& server,
                           std::shared_ptr<ServiceManager> service_manager,
                           std::shared_ptr<HealthMonitor> health_monitor,
                           std::shared_ptr<ConfigManager> config_manager) {
    auto endpoints = std::make_unique<SystemEndpoints>(service_manager, health_monitor, config_manager);
    endpoints->register_endpoints(server);
}

Json get_api_documentation() {
    Json doc = {
        {"title", "QuantServices System API"},
        {"version", "1.0.0"},
        {"description", "System management and monitoring API"},
        {"base_url", "/api/v1/system"},
        {"endpoints", Json::array()}
    };
    return doc;
}

Json get_openapi_specification() {
    Json spec = {
        {"openapi", "3.0.0"},
        {"info", {
            {"title", "QuantServices API"},
            {"version", "1.0.0"},
            {"description", "Integrated DataHub and Trading Services API"}
        }},
        {"servers", Json::array({
            {{"url", "http://localhost:8080/api/v1"}}
        })},
        {"paths", Json::object()}
    };
    return spec;
}

Json get_configuration_schema() {
    Json schema = {
        {"type", "object"},
        {"properties", {
            {"server", {
                {"type", "object"},
                {"properties", {
                    {"host", {"type", "string"}},
                    {"port", {"type", "integer"}},
                    {"worker_threads", {"type", "integer"}}
                }}
            }},
            {"services", {
                {"type", "object"},
                {"properties", {
                    {"datahub", {
                        {"type", "object"},
                        {"properties", {
                            {"enabled", {"type", "boolean"}},
                            {"config_file", {"type", "string"}}
                        }}
                    }},
                    {"trading", {
                        {"type", "object"},
                        {"properties", {
                            {"enabled", {"type", "boolean"}},
                            {"config_file", {"type", "string"}}
                        }}
                    }}
                }}
            }}
        }}
    };
    return schema;
}

} // namespace SystemEndpointHelper

} // namespace QuantServices
