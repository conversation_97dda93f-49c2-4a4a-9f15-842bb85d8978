/**
 * @file HealthMonitor.cpp
 * @brief Implementation of health monitoring system
 * <AUTHOR> Team
 * @date 2024
 */

#include "quantservices/HealthMonitor.h"
#include <spdlog/spdlog.h>
#include <chrono>
#include <thread>

#ifdef _WIN32
#include <windows.h>
#include <psapi.h>
#include <pdh.h>
#else
#include <sys/sysinfo.h>
#include <sys/statvfs.h>
#include <unistd.h>
#include <fstream>
#include <sstream>
#endif

namespace QuantServices {

// Utility functions

std::string to_string(HealthStatus status) {
    switch (status) {
        case HealthStatus::Healthy: return "healthy";
        case HealthStatus::Warning: return "warning";
        case HealthStatus::Critical: return "critical";
        case HealthStatus::Unknown: return "unknown";
        default: return "unknown";
    }
}

// HealthCheckResult implementation

Json HealthCheckResult::to_json() const {
    Json j;
    j["component"] = component;
    j["status"] = to_string(status);
    j["message"] = message;
    j["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        timestamp.time_since_epoch()).count();
    j["response_time_ms"] = response_time.count();
    j["metadata"] = metadata;
    return j;
}

// SystemMetrics implementation

Json SystemMetrics::to_json() const {
    Json j;
    j["cpu"] = {
        {"usage_percent", cpu_usage_percent},
        {"load_average", cpu_load_average}
    };
    j["memory"] = {
        {"total_bytes", memory_total_bytes},
        {"used_bytes", memory_used_bytes},
        {"available_bytes", memory_available_bytes},
        {"usage_percent", memory_usage_percent}
    };
    j["disk"] = {
        {"total_bytes", disk_total_bytes},
        {"used_bytes", disk_used_bytes},
        {"available_bytes", disk_available_bytes},
        {"usage_percent", disk_usage_percent}
    };
    j["network"] = {
        {"bytes_sent", network_bytes_sent},
        {"bytes_received", network_bytes_received},
        {"packets_sent", network_packets_sent},
        {"packets_received", network_packets_received}
    };
    j["process"] = {
        {"id", process_id},
        {"memory_bytes", process_memory_bytes},
        {"cpu_percent", process_cpu_percent},
        {"start_time", std::chrono::duration_cast<std::chrono::milliseconds>(
            process_start_time.time_since_epoch()).count()}
    };
    j["application"] = {
        {"total_requests", total_requests},
        {"successful_requests", successful_requests},
        {"failed_requests", failed_requests},
        {"average_response_time_ms", average_response_time_ms}
    };
    j["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        timestamp.time_since_epoch()).count();
    return j;
}

// HealthMonitorConfig implementation

Json HealthMonitorConfig::to_json() const {
    Json j;
    j["check_interval_seconds"] = check_interval.count();
    j["metrics_interval_seconds"] = metrics_interval.count();
    j["enable_system_metrics"] = enable_system_metrics;
    j["enable_application_metrics"] = enable_application_metrics;
    j["enable_alerts"] = enable_alerts;
    j["alert_channels"] = alert_channels;
    j["thresholds"] = {
        {"cpu_warning_threshold", cpu_warning_threshold},
        {"cpu_critical_threshold", cpu_critical_threshold},
        {"memory_warning_threshold", memory_warning_threshold},
        {"memory_critical_threshold", memory_critical_threshold},
        {"disk_warning_threshold", disk_warning_threshold},
        {"disk_critical_threshold", disk_critical_threshold},
        {"response_time_warning_threshold", response_time_warning_threshold},
        {"response_time_critical_threshold", response_time_critical_threshold}
    };
    return j;
}

HealthMonitorConfig HealthMonitorConfig::from_json(const Json& j) {
    HealthMonitorConfig config;
    
    if (j.contains("check_interval_seconds")) {
        config.check_interval = std::chrono::seconds(j["check_interval_seconds"].get<int>());
    }
    if (j.contains("metrics_interval_seconds")) {
        config.metrics_interval = std::chrono::seconds(j["metrics_interval_seconds"].get<int>());
    }
    if (j.contains("enable_system_metrics")) {
        config.enable_system_metrics = j["enable_system_metrics"];
    }
    if (j.contains("enable_application_metrics")) {
        config.enable_application_metrics = j["enable_application_metrics"];
    }
    if (j.contains("enable_alerts")) {
        config.enable_alerts = j["enable_alerts"];
    }
    if (j.contains("alert_channels")) {
        config.alert_channels = j["alert_channels"].get<std::vector<std::string>>();
    }
    
    if (j.contains("thresholds")) {
        const auto& thresholds = j["thresholds"];
        if (thresholds.contains("cpu_warning_threshold")) {
            config.cpu_warning_threshold = thresholds["cpu_warning_threshold"];
        }
        if (thresholds.contains("cpu_critical_threshold")) {
            config.cpu_critical_threshold = thresholds["cpu_critical_threshold"];
        }
        if (thresholds.contains("memory_warning_threshold")) {
            config.memory_warning_threshold = thresholds["memory_warning_threshold"];
        }
        if (thresholds.contains("memory_critical_threshold")) {
            config.memory_critical_threshold = thresholds["memory_critical_threshold"];
        }
        if (thresholds.contains("disk_warning_threshold")) {
            config.disk_warning_threshold = thresholds["disk_warning_threshold"];
        }
        if (thresholds.contains("disk_critical_threshold")) {
            config.disk_critical_threshold = thresholds["disk_critical_threshold"];
        }
        if (thresholds.contains("response_time_warning_threshold")) {
            config.response_time_warning_threshold = thresholds["response_time_warning_threshold"];
        }
        if (thresholds.contains("response_time_critical_threshold")) {
            config.response_time_critical_threshold = thresholds["response_time_critical_threshold"];
        }
    }
    
    return config;
}

// HealthMonitor implementation

HealthMonitor::HealthMonitor(const HealthMonitorConfig& config)
    : config_(config)
    , logger_(spdlog::get("quantservices") ? spdlog::get("quantservices") : spdlog::default_logger()) {
    
    logger_->debug("HealthMonitor initialized");
}

HealthMonitor::~HealthMonitor() {
    stop();
    logger_->debug("HealthMonitor destroyed");
}

bool HealthMonitor::start() {
    if (running_.load()) {
        logger_->warn("Health monitor is already running");
        return true;
    }
    
    try {
        running_.store(true);
        
        // Start health check thread
        health_check_thread_ = std::make_unique<std::thread>(&HealthMonitor::health_check_loop, this);
        
        // Start metrics collection thread
        if (config_.enable_system_metrics || config_.enable_application_metrics) {
            metrics_thread_ = std::make_unique<std::thread>(&HealthMonitor::metrics_collection_loop, this);
        }
        
        logger_->info("Health monitor started");
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to start health monitor: {}", e.what());
        running_.store(false);
        return false;
    }
}

void HealthMonitor::stop() {
    if (!running_.load()) {
        return;
    }
    
    logger_->info("Stopping health monitor...");
    running_.store(false);
    
    // Join threads
    if (health_check_thread_ && health_check_thread_->joinable()) {
        health_check_thread_->join();
    }
    health_check_thread_.reset();
    
    if (metrics_thread_ && metrics_thread_->joinable()) {
        metrics_thread_->join();
    }
    metrics_thread_.reset();
    
    logger_->info("Health monitor stopped");
}

bool HealthMonitor::is_running() const {
    return running_.load();
}

void HealthMonitor::register_health_check(const std::string& component, HealthCheckFunction check_func) {
    std::unique_lock lock(health_checks_mutex_);
    health_checks_[component] = std::move(check_func);
    logger_->debug("Registered health check for component: {}", component);
}

void HealthMonitor::unregister_health_check(const std::string& component) {
    std::unique_lock lock(health_checks_mutex_);
    health_checks_.erase(component);
    logger_->debug("Unregistered health check for component: {}", component);
}

HealthCheckResult HealthMonitor::run_health_check(const std::string& component) {
    std::shared_lock lock(health_checks_mutex_);
    
    auto it = health_checks_.find(component);
    if (it == health_checks_.end()) {
        return HealthCheckResult{
            component,
            HealthStatus::Unknown,
            "Health check not registered",
            std::chrono::system_clock::now(),
            std::chrono::milliseconds(0),
            Json::object()
        };
    }
    
    try {
        auto start_time = std::chrono::steady_clock::now();
        auto result = it->second();
        auto end_time = std::chrono::steady_clock::now();
        
        result.response_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        return result;
        
    } catch (const std::exception& e) {
        return HealthCheckResult{
            component,
            HealthStatus::Critical,
            "Health check failed: " + std::string(e.what()),
            std::chrono::system_clock::now(),
            std::chrono::milliseconds(0),
            Json::object()
        };
    }
}

std::vector<HealthCheckResult> HealthMonitor::run_all_health_checks() {
    std::vector<HealthCheckResult> results;
    
    std::shared_lock lock(health_checks_mutex_);
    for (const auto& [component, check_func] : health_checks_) {
        lock.unlock();
        results.push_back(run_health_check(component));
        lock.lock();
    }
    
    return results;
}

HealthStatus HealthMonitor::get_overall_health_status() const {
    std::lock_guard lock(results_mutex_);
    return evaluate_health_status(latest_results_);
}

std::vector<HealthCheckResult> HealthMonitor::get_latest_results() const {
    std::lock_guard lock(results_mutex_);
    return latest_results_;
}

SystemMetrics HealthMonitor::get_system_metrics() const {
    if (!config_.enable_system_metrics) {
        return SystemMetrics{};
    }
    
    return collect_system_metrics();
}

std::vector<SystemMetrics> HealthMonitor::get_historical_metrics(std::chrono::minutes duration) const {
    std::lock_guard lock(metrics_mutex_);
    
    std::vector<SystemMetrics> result;
    auto cutoff_time = std::chrono::system_clock::now() - duration;
    
    for (const auto& metrics : metrics_history_) {
        if (metrics.timestamp >= cutoff_time) {
            result.push_back(metrics);
        }
    }
    
    return result;
}

void HealthMonitor::set_health_event_callback(HealthEventCallback callback) {
    std::lock_guard lock(event_callback_mutex_);
    event_callback_ = std::move(callback);
}

Json HealthMonitor::get_health_summary() const {
    auto results = get_latest_results();
    auto overall_status = evaluate_health_status(results);
    
    Json summary = {
        {"overall_status", to_string(overall_status)},
        {"total_checks", results.size()},
        {"healthy_checks", 0},
        {"warning_checks", 0},
        {"critical_checks", 0},
        {"unknown_checks", 0},
        {"timestamp", std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count()}
    };
    
    for (const auto& result : results) {
        switch (result.status) {
            case HealthStatus::Healthy:
                summary["healthy_checks"] = summary["healthy_checks"].get<int>() + 1;
                break;
            case HealthStatus::Warning:
                summary["warning_checks"] = summary["warning_checks"].get<int>() + 1;
                break;
            case HealthStatus::Critical:
                summary["critical_checks"] = summary["critical_checks"].get<int>() + 1;
                break;
            case HealthStatus::Unknown:
                summary["unknown_checks"] = summary["unknown_checks"].get<int>() + 1;
                break;
        }
    }
    
    return summary;
}

Json HealthMonitor::get_detailed_health_report() const {
    auto results = get_latest_results();
    auto overall_status = evaluate_health_status(results);
    auto system_metrics = get_system_metrics();
    
    Json report = {
        {"overall_status", to_string(overall_status)},
        {"timestamp", std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count()},
        {"health_checks", Json::array()},
        {"system_metrics", system_metrics.to_json()},
        {"configuration", config_.to_json()}
    };
    
    for (const auto& result : results) {
        report["health_checks"].push_back(result.to_json());
    }
    
    return report;
}

void HealthMonitor::update_config(const HealthMonitorConfig& config) {
    std::lock_guard lock(config_mutex_);
    config_ = config;
    logger_->info("Health monitor configuration updated");
}

const HealthMonitorConfig& HealthMonitor::get_config() const {
    std::lock_guard lock(config_mutex_);
    return config_;
}

// Private methods implementation

void HealthMonitor::health_check_loop() {
    while (running_.load()) {
        try {
            auto results = run_all_health_checks();

            {
                std::lock_guard lock(results_mutex_);
                latest_results_ = results;
            }

            // Notify event callback for critical/warning results
            for (const auto& result : results) {
                if (result.status == HealthStatus::Critical || result.status == HealthStatus::Warning) {
                    notify_health_event(result);
                }
            }

            // Sleep for check interval
            std::this_thread::sleep_for(config_.check_interval);

        } catch (const std::exception& e) {
            logger_->error("Exception in health check loop: {}", e.what());
            std::this_thread::sleep_for(std::chrono::seconds(5));
        }
    }
}

void HealthMonitor::metrics_collection_loop() {
    while (running_.load()) {
        try {
            if (config_.enable_system_metrics) {
                auto metrics = collect_system_metrics();
                store_metrics(metrics);
            }

            // Sleep for metrics interval
            std::this_thread::sleep_for(config_.metrics_interval);

        } catch (const std::exception& e) {
            logger_->error("Exception in metrics collection loop: {}", e.what());
            std::this_thread::sleep_for(std::chrono::seconds(10));
        }
    }
}

SystemMetrics HealthMonitor::collect_system_metrics() const {
    SystemMetrics metrics;
    metrics.timestamp = std::chrono::system_clock::now();

    try {
        // CPU metrics
        metrics.cpu_usage_percent = get_cpu_usage();

        // Memory metrics
        metrics.memory_used_bytes = get_memory_usage();
        metrics.memory_total_bytes = get_total_memory();
        metrics.memory_available_bytes = metrics.memory_total_bytes - metrics.memory_used_bytes;
        if (metrics.memory_total_bytes > 0) {
            metrics.memory_usage_percent = (static_cast<double>(metrics.memory_used_bytes) /
                                           metrics.memory_total_bytes) * 100.0;
        }

        // Disk metrics
        metrics.disk_used_bytes = get_disk_usage();
        metrics.disk_total_bytes = get_total_disk_space();
        metrics.disk_available_bytes = metrics.disk_total_bytes - metrics.disk_used_bytes;
        if (metrics.disk_total_bytes > 0) {
            metrics.disk_usage_percent = (static_cast<double>(metrics.disk_used_bytes) /
                                         metrics.disk_total_bytes) * 100.0;
        }

        // Process metrics
        metrics.process_id = static_cast<uint32_t>(getpid());
        metrics.process_start_time = std::chrono::system_clock::now(); // Placeholder

    } catch (const std::exception& e) {
        logger_->error("Failed to collect system metrics: {}", e.what());
    }

    return metrics;
}

void HealthMonitor::store_metrics(const SystemMetrics& metrics) {
    std::lock_guard lock(metrics_mutex_);

    metrics_history_.push_back(metrics);

    // Keep only recent metrics
    if (metrics_history_.size() > MAX_METRICS_HISTORY) {
        metrics_history_.erase(metrics_history_.begin());
    }
}

void HealthMonitor::cleanup_old_metrics() {
    std::lock_guard lock(metrics_mutex_);

    auto cutoff_time = std::chrono::system_clock::now() - std::chrono::hours(24);

    metrics_history_.erase(
        std::remove_if(metrics_history_.begin(), metrics_history_.end(),
                      [cutoff_time](const SystemMetrics& metrics) {
                          return metrics.timestamp < cutoff_time;
                      }),
        metrics_history_.end());
}

HealthStatus HealthMonitor::evaluate_health_status(const std::vector<HealthCheckResult>& results) const {
    if (results.empty()) {
        return HealthStatus::Unknown;
    }

    bool has_critical = false;
    bool has_warning = false;
    bool has_unknown = false;

    for (const auto& result : results) {
        switch (result.status) {
            case HealthStatus::Critical:
                has_critical = true;
                break;
            case HealthStatus::Warning:
                has_warning = true;
                break;
            case HealthStatus::Unknown:
                has_unknown = true;
                break;
            case HealthStatus::Healthy:
                // Continue checking
                break;
        }
    }

    if (has_critical) {
        return HealthStatus::Critical;
    } else if (has_warning) {
        return HealthStatus::Warning;
    } else if (has_unknown) {
        return HealthStatus::Unknown;
    } else {
        return HealthStatus::Healthy;
    }
}

void HealthMonitor::notify_health_event(const HealthCheckResult& result) {
    std::lock_guard lock(event_callback_mutex_);
    if (event_callback_) {
        try {
            event_callback_(result);
        } catch (const std::exception& e) {
            logger_->error("Exception in health event callback: {}", e.what());
        }
    }
}

// Platform-specific system metrics implementation

double HealthMonitor::get_cpu_usage() const {
#ifdef _WIN32
    return get_cpu_usage_windows();
#else
    return get_cpu_usage_linux();
#endif
}

uint64_t HealthMonitor::get_memory_usage() const {
#ifdef _WIN32
    return get_memory_usage_windows();
#else
    return get_memory_usage_linux();
#endif
}

uint64_t HealthMonitor::get_total_memory() const {
#ifdef _WIN32
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    GlobalMemoryStatusEx(&memInfo);
    return memInfo.ullTotalPhys;
#else
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
        return info.totalram * info.mem_unit;
    }
    return 0;
#endif
}

uint64_t HealthMonitor::get_disk_usage() const {
#ifdef _WIN32
    return get_disk_usage_windows();
#else
    return get_disk_usage_linux();
#endif
}

uint64_t HealthMonitor::get_total_disk_space() const {
#ifdef _WIN32
    ULARGE_INTEGER freeBytesAvailable, totalNumberOfBytes, totalNumberOfFreeBytes;
    if (GetDiskFreeSpaceEx(L"C:\\", &freeBytesAvailable, &totalNumberOfBytes, &totalNumberOfFreeBytes)) {
        return totalNumberOfBytes.QuadPart;
    }
    return 0;
#else
    struct statvfs stat;
    if (statvfs("/", &stat) == 0) {
        return stat.f_blocks * stat.f_frsize;
    }
    return 0;
#endif
}

#ifdef _WIN32
double HealthMonitor::get_cpu_usage_windows() const {
    // Simplified CPU usage calculation for Windows
    // In a real implementation, you would use PDH (Performance Data Helper) API
    return 50.0; // Placeholder
}

uint64_t HealthMonitor::get_memory_usage_windows() const {
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        return pmc.WorkingSetSize;
    }
    return 0;
}

uint64_t HealthMonitor::get_disk_usage_windows() const {
    ULARGE_INTEGER freeBytesAvailable, totalNumberOfBytes, totalNumberOfFreeBytes;
    if (GetDiskFreeSpaceEx(L"C:\\", &freeBytesAvailable, &totalNumberOfBytes, &totalNumberOfFreeBytes)) {
        return totalNumberOfBytes.QuadPart - totalNumberOfFreeBytes.QuadPart;
    }
    return 0;
}
#else
double HealthMonitor::get_cpu_usage_linux() const {
    // Simplified CPU usage calculation for Linux
    // In a real implementation, you would read from /proc/stat
    return 50.0; // Placeholder
}

uint64_t HealthMonitor::get_memory_usage_linux() const {
    std::ifstream file("/proc/self/status");
    std::string line;

    while (std::getline(file, line)) {
        if (line.substr(0, 6) == "VmRSS:") {
            std::istringstream iss(line);
            std::string label;
            uint64_t value;
            std::string unit;
            iss >> label >> value >> unit;

            // Convert from kB to bytes
            return value * 1024;
        }
    }

    return 0;
}

uint64_t HealthMonitor::get_disk_usage_linux() const {
    struct statvfs stat;
    if (statvfs("/", &stat) == 0) {
        return (stat.f_blocks - stat.f_bavail) * stat.f_frsize;
    }
    return 0;
}
#endif

// Built-in health check functions implementation

namespace BuiltinHealthChecks {

HealthCheckFunction connectivity_check() {
    return []() -> HealthCheckResult {
        return HealthCheckResult{
            "connectivity",
            HealthStatus::Healthy,
            "Basic connectivity check passed",
            std::chrono::system_clock::now(),
            std::chrono::milliseconds(1),
            Json::object()
        };
    };
}

HealthCheckFunction database_check(const std::string& connection_string) {
    return [connection_string]() -> HealthCheckResult {
        // Placeholder implementation
        // In a real implementation, this would test database connectivity
        return HealthCheckResult{
            "database",
            HealthStatus::Healthy,
            "Database connectivity check passed",
            std::chrono::system_clock::now(),
            std::chrono::milliseconds(10),
            Json{{"connection_string", connection_string}}
        };
    };
}

HealthCheckFunction service_dependency_check(const std::string& service_url) {
    return [service_url]() -> HealthCheckResult {
        // Placeholder implementation
        // In a real implementation, this would make HTTP request to service
        return HealthCheckResult{
            "service_dependency",
            HealthStatus::Healthy,
            "Service dependency check passed",
            std::chrono::system_clock::now(),
            std::chrono::milliseconds(50),
            Json{{"service_url", service_url}}
        };
    };
}

HealthCheckFunction memory_usage_check(double warning_threshold, double critical_threshold) {
    return [warning_threshold, critical_threshold]() -> HealthCheckResult {
        HealthMonitor temp_monitor;
        auto metrics = temp_monitor.get_system_metrics();

        HealthStatus status = HealthStatus::Healthy;
        std::string message = "Memory usage is normal";

        if (metrics.memory_usage_percent >= critical_threshold) {
            status = HealthStatus::Critical;
            message = "Memory usage is critically high";
        } else if (metrics.memory_usage_percent >= warning_threshold) {
            status = HealthStatus::Warning;
            message = "Memory usage is high";
        }

        return HealthCheckResult{
            "memory_usage",
            status,
            message,
            std::chrono::system_clock::now(),
            std::chrono::milliseconds(5),
            Json{
                {"usage_percent", metrics.memory_usage_percent},
                {"warning_threshold", warning_threshold},
                {"critical_threshold", critical_threshold}
            }
        };
    };
}

HealthCheckFunction disk_space_check(const std::string& path, double warning_threshold, double critical_threshold) {
    return [path, warning_threshold, critical_threshold]() -> HealthCheckResult {
        HealthMonitor temp_monitor;
        auto metrics = temp_monitor.get_system_metrics();

        HealthStatus status = HealthStatus::Healthy;
        std::string message = "Disk space is sufficient";

        if (metrics.disk_usage_percent >= critical_threshold) {
            status = HealthStatus::Critical;
            message = "Disk space is critically low";
        } else if (metrics.disk_usage_percent >= warning_threshold) {
            status = HealthStatus::Warning;
            message = "Disk space is low";
        }

        return HealthCheckResult{
            "disk_space",
            status,
            message,
            std::chrono::system_clock::now(),
            std::chrono::milliseconds(5),
            Json{
                {"path", path},
                {"usage_percent", metrics.disk_usage_percent},
                {"warning_threshold", warning_threshold},
                {"critical_threshold", critical_threshold}
            }
        };
    };
}

} // namespace BuiltinHealthChecks

} // namespace QuantServices
