#pragma once

#include "IDataRepository_Simple.h"
#include <memory>

namespace DataHub::Data {

// SQLite database repository implementation
class SqliteRepository : public IQuoteRepository, public IBarRepository, public ITickRepository, public ISecurityRepository {
public:
    explicit SqliteRepository(const std::string& db_path);
    ~SqliteRepository();

    // IDataRepository interface
    Core::Result<void> connect() override;
    Core::Result<void> disconnect() override;
    bool is_connected() const noexcept override;
    Core::Result<void> begin_transaction() override;
    Core::Result<void> commit_transaction() override;
    Core::Result<void> rollback_transaction() override;
    Core::Result<bool> health_check() override;

    // IQuoteRepository interface
    Core::Result<Core::QuoteData> get_latest_quote(const Core::Symbol& symbol) override;
    Core::Result<Core::QuoteDataVector> get_quotes(const QueryCondition& condition) override;
    Core::Result<void> save_quote(const Core::QuoteData& quote) override;
    Core::Result<void> save_quotes(Core::QuoteDataSpanConst quotes) override;
    AsyncTask<Core::QuoteData> get_latest_quote_async(const Core::Symbol& symbol) override;
    AsyncTask<Core::QuoteDataVector> get_quotes_async(const QueryCondition& condition) override;
    AsyncTask<void> save_quote_async(const Core::QuoteData& quote) override;
    AsyncTask<void> save_quotes_async(Core::QuoteDataSpanConst quotes) override;
    Core::Result<void> batch_save_quotes(Core::QuoteDataSpanConst quotes) override;
    Core::Result<std::size_t> delete_quotes(const QueryCondition& condition) override;
    Core::Result<std::size_t> count_quotes(const QueryCondition& condition) override;

    // IBarRepository interface
    Core::Result<Core::BarDataVector> get_bars(const QueryCondition& condition) override;
    Core::Result<Core::BarData> get_latest_bar(const Core::Symbol& symbol, Core::BarSize bar_size, Core::BarType bar_type) override;
    Core::Result<void> save_bar(const Core::BarData& bar) override;
    Core::Result<void> save_bars(Core::BarDataSpanConst bars) override;
    Core::Result<void> update_bar(const Core::BarData& bar) override;
    AsyncTask<Core::BarDataVector> get_bars_async(const QueryCondition& condition) override;
    AsyncTask<Core::BarData> get_latest_bar_async(const Core::Symbol& symbol, Core::BarSize bar_size, Core::BarType bar_type) override;
    AsyncTask<void> save_bar_async(const Core::BarData& bar) override;
    AsyncTask<void> save_bars_async(Core::BarDataSpanConst bars) override;
    Core::Result<Core::BarDataVector> aggregate_bars(const Core::Symbol& symbol, Core::BarSize from_size, Core::BarSize to_size, const QueryCondition& condition) override;
    Core::Result<void> batch_save_bars(Core::BarDataSpanConst bars) override;
    Core::Result<std::size_t> delete_bars(const QueryCondition& condition) override;
    Core::Result<std::size_t> count_bars(const QueryCondition& condition) override;

    // ITickRepository interface (simplified implementation)
    Core::Result<Core::TickDataVector> get_ticks(const QueryCondition& condition) override;
    Core::Result<void> save_tick(const Core::TickData& tick) override;
    Core::Result<void> save_ticks(Core::TickDataSpanConst ticks) override;
    AsyncTask<Core::TickDataVector> get_ticks_async(const QueryCondition& condition) override;
    AsyncTask<void> save_tick_async(const Core::TickData& tick) override;
    AsyncTask<void> save_ticks_async(Core::TickDataSpanConst ticks) override;
    Core::Result<void> batch_save_ticks(Core::TickDataSpanConst ticks) override;
    Core::Result<std::size_t> delete_ticks(const QueryCondition& condition) override;
    Core::Result<std::size_t> count_ticks(const QueryCondition& condition) override;

    // ISecurityRepository interface (simplified implementation)
    Core::Result<Core::SecurityInfo> get_security(const Core::Symbol& symbol) override;
    Core::Result<Core::SecurityInfoVector> get_securities(const QueryCondition& condition) override;
    Core::Result<void> save_security(const Core::SecurityInfo& security) override;
    Core::Result<void> update_security(const Core::SecurityInfo& security) override;
    Core::Result<void> delete_security(const Core::Symbol& symbol) override;
    Core::Result<Core::BlockInfo> get_block(const std::string& name) override;
    Core::Result<Core::BlockInfoVector> get_blocks() override;
    Core::Result<void> save_block(const Core::BlockInfo& block) override;
    Core::Result<void> update_block(const Core::BlockInfo& block) override;
    Core::Result<void> delete_block(const std::string& name) override;
    AsyncTask<Core::SecurityInfo> get_security_async(const Core::Symbol& symbol) override;
    AsyncTask<Core::SecurityInfoVector> get_securities_async(const QueryCondition& condition) override;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl_;
};

} // namespace DataHub::Data
