# Examples
cmake_minimum_required(VERSION 3.20)

# Basic example
add_executable(basic_example
    basic_example.cpp
)

target_link_libraries(basic_example
    PRIVATE
        datahub_core
)

target_compile_features(basic_example PRIVATE cxx_std_20)

# Quote example
add_executable(quote_example
    quote_example.cpp
)

target_link_libraries(quote_example
    PRIVATE
        datahub_core
)

target_compile_features(quote_example PRIVATE cxx_std_20)

# Bar example
add_executable(bar_example
    bar_example.cpp
)

target_link_libraries(bar_example
    PRIVATE
        datahub_core
)

target_compile_features(bar_example PRIVATE cxx_std_20)

# Market Data examples (if dependencies are available)
if(CURL_FOUND)
    # Simple market data example
    add_executable(simple_market_data
        simple_market_data.cpp
    )

    target_link_libraries(simple_market_data
        PRIVATE
            datahub_services
            $<$<PLATFORM_ID:Windows>:ws2_32>
            $<$<PLATFORM_ID:Windows>:winmm>
            $<$<PLATFORM_ID:Linux>:pthread>
    )

    target_compile_features(simple_market_data PRIVATE cxx_std_20)

    message(STATUS "Market Data examples enabled")
else()
    message(WARNING "Market Data examples disabled due to missing dependencies")
endif()
