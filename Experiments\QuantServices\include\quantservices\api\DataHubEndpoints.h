/**
 * @file DataHubEndpoints.h
 * @brief REST API endpoints for DataHub functionality
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include "../RestApiServer.h"
#include <memory>

// Forward declarations
namespace DataHub {
    namespace Services {
        class IDataHubManager;
    }
    namespace API {
        class DataHubAPI;
    }
}

namespace QuantServices {

/**
 * @brief DataHub API endpoints handler
 */
class DataHubEndpoints {
public:
    /**
     * @brief Constructor
     */
    explicit DataHubEndpoints(std::shared_ptr<DataHub::Services::IDataHubManager> datahub_manager,
                             std::shared_ptr<DataHub::API::DataHubAPI> datahub_api);
    
    /**
     * @brief Destructor
     */
    ~DataHubEndpoints() = default;
    
    // Non-copyable, non-movable
    DataHubEndpoints(const DataHubEndpoints&) = delete;
    DataHubEndpoints& operator=(const DataHubEndpoints&) = delete;
    DataHubEndpoints(DataHubEndpoints&&) = delete;
    DataHubEndpoints& operator=(DataHubEndpoints&&) = delete;
    
    /**
     * @brief Register all DataHub endpoints with the server
     */
    void register_endpoints(RestApiServer& server);

private:
    // DataHub services
    std::shared_ptr<DataHub::Services::IDataHubManager> datahub_manager_;
    std::shared_ptr<DataHub::API::DataHubAPI> datahub_api_;
    
    // Endpoint handlers
    
    // Quote data endpoints
    ApiResponse get_quote(const RequestContext& context);
    ApiResponse get_quotes(const RequestContext& context);
    ApiResponse get_latest_quotes(const RequestContext& context);
    
    // Historical data endpoints
    ApiResponse get_bars(const RequestContext& context);
    ApiResponse get_ticks(const RequestContext& context);
    ApiResponse get_historical_data(const RequestContext& context);
    
    // Security information endpoints
    ApiResponse get_security(const RequestContext& context);
    ApiResponse search_securities(const RequestContext& context);
    ApiResponse get_security_list(const RequestContext& context);
    
    // Market data subscription endpoints
    ApiResponse subscribe_quote(const RequestContext& context);
    ApiResponse unsubscribe_quote(const RequestContext& context);
    ApiResponse get_subscriptions(const RequestContext& context);
    
    // Data management endpoints
    ApiResponse save_quote(const RequestContext& context);
    ApiResponse save_quotes(const RequestContext& context);
    ApiResponse save_bars(const RequestContext& context);
    
    // Statistics and analytics endpoints
    ApiResponse get_market_statistics(const RequestContext& context);
    ApiResponse get_price_statistics(const RequestContext& context);
    ApiResponse get_volume_statistics(const RequestContext& context);
    
    // DataHub status and health endpoints
    ApiResponse get_datahub_status(const RequestContext& context);
    ApiResponse get_datahub_health(const RequestContext& context);
    ApiResponse get_datahub_metrics(const RequestContext& context);
    
    // Utility methods
    bool validate_symbol(const std::string& symbol) const;
    bool validate_time_range(const std::string& start_time, const std::string& end_time) const;
    bool validate_bar_size(const std::string& bar_size) const;
    
    Json symbol_to_json(const std::string& symbol) const;
    Json quote_data_to_json(const Json& quote_data) const;
    Json bar_data_to_json(const Json& bar_data) const;
    Json security_info_to_json(const Json& security_info) const;
    
    std::vector<std::string> parse_symbols_list(const std::string& symbols_param) const;
    std::chrono::system_clock::time_point parse_timestamp(const std::string& timestamp) const;
    
    // Error handling
    ApiResponse create_validation_error(const std::string& field, const std::string& message) const;
    ApiResponse create_not_found_error(const std::string& resource) const;
    ApiResponse create_service_error(const std::string& message) const;
};

/**
 * @brief DataHub endpoint registration helper
 */
namespace DataHubEndpointHelper {

/**
 * @brief Register all DataHub endpoints
 */
void register_all_endpoints(RestApiServer& server, 
                           std::shared_ptr<DataHub::Services::IDataHubManager> datahub_manager,
                           std::shared_ptr<DataHub::API::DataHubAPI> datahub_api);

/**
 * @brief Get DataHub API documentation
 */
Json get_api_documentation();

/**
 * @brief Get DataHub endpoint schemas
 */
Json get_endpoint_schemas();

} // namespace DataHubEndpointHelper

} // namespace QuantServices
