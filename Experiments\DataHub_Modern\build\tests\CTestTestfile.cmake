# CMake generated Testfile for 
# Source directory: E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests
# Build directory: E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
include("E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/datahub_tests_include-b12d07c.cmake")
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[core_types_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Debug/datahub_tests.exe" "[types]")
  set_tests_properties([=[core_types_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;42;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[core_types_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe" "[types]")
  set_tests_properties([=[core_types_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;42;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[core_types_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/MinSizeRel/datahub_tests.exe" "[types]")
  set_tests_properties([=[core_types_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;42;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[core_types_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/RelWithDebInfo/datahub_tests.exe" "[types]")
  set_tests_properties([=[core_types_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;42;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
else()
  add_test([=[core_types_test]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[market_data_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Debug/datahub_tests.exe" "[marketdata]")
  set_tests_properties([=[market_data_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;43;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[market_data_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe" "[marketdata]")
  set_tests_properties([=[market_data_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;43;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[market_data_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/MinSizeRel/datahub_tests.exe" "[marketdata]")
  set_tests_properties([=[market_data_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;43;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[market_data_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/RelWithDebInfo/datahub_tests.exe" "[marketdata]")
  set_tests_properties([=[market_data_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;43;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
else()
  add_test([=[market_data_test]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[security_info_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Debug/datahub_tests.exe" "[security]")
  set_tests_properties([=[security_info_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;44;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[security_info_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe" "[security]")
  set_tests_properties([=[security_info_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;44;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[security_info_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/MinSizeRel/datahub_tests.exe" "[security]")
  set_tests_properties([=[security_info_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;44;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[security_info_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/RelWithDebInfo/datahub_tests.exe" "[security]")
  set_tests_properties([=[security_info_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;44;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
else()
  add_test([=[security_info_test]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[data_aggregation_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Debug/datahub_tests.exe" "[aggregation]")
  set_tests_properties([=[data_aggregation_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;45;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[data_aggregation_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe" "[aggregation]")
  set_tests_properties([=[data_aggregation_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;45;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[data_aggregation_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/MinSizeRel/datahub_tests.exe" "[aggregation]")
  set_tests_properties([=[data_aggregation_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;45;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[data_aggregation_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/RelWithDebInfo/datahub_tests.exe" "[aggregation]")
  set_tests_properties([=[data_aggregation_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;45;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
else()
  add_test([=[data_aggregation_test]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[performance_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Debug/datahub_tests.exe" "[performance]")
  set_tests_properties([=[performance_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;46;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[performance_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe" "[performance]")
  set_tests_properties([=[performance_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;46;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[performance_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/MinSizeRel/datahub_tests.exe" "[performance]")
  set_tests_properties([=[performance_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;46;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[performance_test]=] "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/RelWithDebInfo/datahub_tests.exe" "[performance]")
  set_tests_properties([=[performance_test]=] PROPERTIES  _BACKTRACE_TRIPLES "E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;46;add_test;E:/lab/RoboQuant/src/Experiments/DataHub_Modern/tests/CMakeLists.txt;0;")
else()
  add_test([=[performance_test]=] NOT_AVAILABLE)
endif()
