#pragma once

#include "IMarketDataProvider.h"
#include "../core/Types.h"
#include <memory>
#include <string>
#include <vector>
#include <thread>
#include <atomic>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <concurrentqueue.h>
#include "HighPerformanceDataProcessor.h"

// ???????CTP API????
struct CThostFtdcMdApi;
struct CThostFtdcMdSpi;
struct CThostFtdcDepthMarketDataField;
struct CThostFtdcRspInfoField;
struct CThostFtdcRspUserLoginField;
struct CThostFtdcUserLogoutField;
struct CThostFtdcSpecificInstrumentField;

namespace DataHub::Providers {

// CTP????
struct CtpConfig : public ProviderConfig {
    std::string front_address;      // ?????
    std::string broker_id;          // ??????ID
    std::string user_id;            // ???ID
    std::string password;           // ????
    std::string flow_path;          // ?????????????
    bool using_udp{false};          // ??????UDP
    bool multicast{false};          // ?????????
    std::size_t buffer_size{64};    // ??????????(MB)
    
    CtpConfig() {
        type = ProviderType::CTP_Futures;
        name = "CTP_Futures_Provider";
    }
};

// CTP????????????
class CtpMarketDataProvider : public MarketDataProviderBase {
public:
    explicit CtpMarketDataProvider();
    ~CtpMarketDataProvider() override;
    
    // IMarketDataProvider interface
    Core::Result<void> connect() override;
    Core::Result<void> disconnect() override;
    
    Core::Result<void> subscribe_quote(const Core::Symbol& symbol) override;
    Core::Result<void> subscribe_quotes(const std::vector<Core::Symbol>& symbols) override;
    Core::Result<void> unsubscribe_quote(const Core::Symbol& symbol) override;
    Core::Result<void> unsubscribe_all() override;
    
    Core::Result<Core::QuoteData> get_latest_quote(const Core::Symbol& symbol) override;
    Core::Result<std::vector<Core::QuoteData>> get_quotes(
        const std::vector<Core::Symbol>& symbols) override;
    
    std::future<Core::Result<Core::QuoteData>> get_latest_quote_async(
        const Core::Symbol& symbol) override;
    std::future<Core::Result<std::vector<Core::QuoteData>>> get_quotes_async(
        const std::vector<Core::Symbol>& symbols) override;
    
    Core::Result<bool> health_check() override;
    std::string get_version() const noexcept override;
    
    // CTP???????
    const char* get_trading_day() const;
    Core::Result<void> set_heartbeat_timeout(std::uint32_t timeout);
    Core::Result<void> set_udp_buffer_size(std::uint32_t size);

protected:
    // MarketDataProviderBase interface
    Core::Result<void> do_connect() override;
    Core::Result<void> do_disconnect() override;
    Core::Result<void> do_subscribe(const Core::Symbol& symbol) override;
    Core::Result<void> do_unsubscribe(const Core::Symbol& symbol) override;
    Core::Result<void> do_start() override;
    Core::Result<void> do_stop() override;

private:
    // CTP API???
    CThostFtdcMdApi* md_api_;
    std::unique_ptr<CThostFtdcMdSpi> md_spi_;
    
    // ????
    std::shared_ptr<CtpConfig> ctp_config_;
    
    // ???????
    void start_data_processing();
    void stop_data_processing();
    void process_market_data();
    void process_tick_data();
    
    // ??????? - ??????????????????
    moodycamel::ConcurrentQueue<CThostFtdcDepthMarketDataField> market_data_queue_;
    std::atomic<bool> has_data_{false};  // ?????????

    // ??????????????????
    std::unique_ptr<QuoteDataProcessor> quote_processor_;

    // ???????
    std::thread data_processing_thread_;
    std::thread tick_processing_thread_;
    std::atomic<bool> stop_processing_{false};
    
    // ?????
    std::atomic<bool> logged_in_{false};
    std::atomic<int> request_id_{0};
    
    // ???????
    std::string symbol_to_instrument_id(const Core::Symbol& symbol) const;
    Core::Symbol instrument_id_to_symbol(const std::string& instrument_id) const;
    
    // ???????
    Core::QuoteData convert_market_data(const CThostFtdcDepthMarketDataField& md) const;
    Core::TickData convert_tick_data(const CThostFtdcDepthMarketDataField& md) const;
    
    // ??????
    void handle_error(const std::string& function, CThostFtdcRspInfoField* error_info);
    bool is_error_response(CThostFtdcRspInfoField* error_info) const;
    
    // ????ID????
    int get_next_request_id();
    
    // ???????????????CTP?????
    friend class CtpMdSpiImpl;
};

// CTP????????????
class CtpMdSpiImpl : public CThostFtdcMdSpi {
public:
    explicit CtpMdSpiImpl(CtpMarketDataProvider* provider);
    ~CtpMdSpiImpl() override = default;
    
    // CThostFtdcMdSpi interface
    void OnFrontConnected() override;
    void OnFrontDisconnected(int reason) override;
    void OnHeartBeatWarning(int time_lapse) override;
    
    void OnRspUserLogin(CThostFtdcRspUserLoginField* login_field,
                       CThostFtdcRspInfoField* error_info,
                       int request_id, bool is_last) override;
    
    void OnRspUserLogout(CThostFtdcUserLogoutField* logout_field,
                        CThostFtdcRspInfoField* error_info,
                        int request_id, bool is_last) override;
    
    void OnRspError(CThostFtdcRspInfoField* error_info,
                   int request_id, bool is_last) override;
    
    void OnRspSubMarketData(CThostFtdcSpecificInstrumentField* instrument,
                           CThostFtdcRspInfoField* error_info,
                           int request_id, bool is_last) override;
    
    void OnRspUnSubMarketData(CThostFtdcSpecificInstrumentField* instrument,
                             CThostFtdcRspInfoField* error_info,
                             int request_id, bool is_last) override;
    
    void OnRtnDepthMarketData(CThostFtdcDepthMarketDataField* market_data) override;

private:
    CtpMarketDataProvider* provider_;
    
    // ????????
    std::string get_disconnect_reason(int reason) const;
    void log_error(const std::string& function, CThostFtdcRspInfoField* error_info) const;
};

// CTP???????
class CtpProviderFactory : public IProviderFactory {
public:
    std::unique_ptr<IMarketDataProvider> create_provider(
        ProviderType type, 
        std::shared_ptr<ProviderConfig> config) override;
    
    std::vector<ProviderType> get_supported_types() const override;
    bool supports_type(ProviderType type) const override;
    
    // CTP???????????
    static std::unique_ptr<CtpMarketDataProvider> create_ctp_provider(
        std::shared_ptr<CtpConfig> config);
};

} // namespace DataHub::Providers
