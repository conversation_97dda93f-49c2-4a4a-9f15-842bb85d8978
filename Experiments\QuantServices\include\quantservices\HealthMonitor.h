/**
 * @file HealthMonitor.h
 * @brief Health monitoring and system metrics for QuantServices
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include <memory>
#include <string>
#include <atomic>
#include <mutex>
#include <thread>
#include <chrono>
#include <functional>
#include <unordered_map>
#include <vector>

#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>

namespace QuantServices {

using Json = nlohmann::json;

/**
 * @brief Health status enumeration
 */
enum class HealthStatus {
    Healthy,
    Warning,
    Critical,
    Unknown
};

/**
 * @brief Convert health status to string
 */
std::string to_string(HealthStatus status);

/**
 * @brief Health check result
 */
struct HealthCheckResult {
    std::string component;
    HealthStatus status;
    std::string message;
    std::chrono::system_clock::time_point timestamp;
    std::chrono::milliseconds response_time;
    Json metadata;
    
    Json to_json() const;
};

/**
 * @brief System metrics structure
 */
struct SystemMetrics {
    // CPU metrics
    double cpu_usage_percent{0.0};
    double cpu_load_average{0.0};
    
    // Memory metrics
    uint64_t memory_total_bytes{0};
    uint64_t memory_used_bytes{0};
    uint64_t memory_available_bytes{0};
    double memory_usage_percent{0.0};
    
    // Disk metrics
    uint64_t disk_total_bytes{0};
    uint64_t disk_used_bytes{0};
    uint64_t disk_available_bytes{0};
    double disk_usage_percent{0.0};
    
    // Network metrics
    uint64_t network_bytes_sent{0};
    uint64_t network_bytes_received{0};
    uint64_t network_packets_sent{0};
    uint64_t network_packets_received{0};
    
    // Process metrics
    uint32_t process_id{0};
    uint64_t process_memory_bytes{0};
    double process_cpu_percent{0.0};
    std::chrono::system_clock::time_point process_start_time;
    
    // Application metrics
    uint64_t total_requests{0};
    uint64_t successful_requests{0};
    uint64_t failed_requests{0};
    double average_response_time_ms{0.0};
    
    std::chrono::system_clock::time_point timestamp;
    
    Json to_json() const;
};

/**
 * @brief Health check function type
 */
using HealthCheckFunction = std::function<HealthCheckResult()>;

/**
 * @brief Health event callback type
 */
using HealthEventCallback = std::function<void(const HealthCheckResult& result)>;

/**
 * @brief Health monitor configuration
 */
struct HealthMonitorConfig {
    std::chrono::seconds check_interval{30};
    std::chrono::seconds metrics_interval{60};
    bool enable_system_metrics{true};
    bool enable_application_metrics{true};
    bool enable_alerts{true};
    std::vector<std::string> alert_channels;
    
    // Thresholds
    double cpu_warning_threshold{80.0};
    double cpu_critical_threshold{95.0};
    double memory_warning_threshold{80.0};
    double memory_critical_threshold{95.0};
    double disk_warning_threshold{85.0};
    double disk_critical_threshold{95.0};
    double response_time_warning_threshold{1000.0}; // ms
    double response_time_critical_threshold{5000.0}; // ms
    
    Json to_json() const;
    static HealthMonitorConfig from_json(const Json& j);
};

/**
 * @brief Main health monitor class
 */
class HealthMonitor {
public:
    /**
     * @brief Constructor
     */
    explicit HealthMonitor(const HealthMonitorConfig& config = HealthMonitorConfig{});
    
    /**
     * @brief Destructor
     */
    ~HealthMonitor();
    
    // Non-copyable, non-movable
    HealthMonitor(const HealthMonitor&) = delete;
    HealthMonitor& operator=(const HealthMonitor&) = delete;
    HealthMonitor(HealthMonitor&&) = delete;
    HealthMonitor& operator=(HealthMonitor&&) = delete;
    
    /**
     * @brief Start health monitoring
     */
    bool start();
    
    /**
     * @brief Stop health monitoring
     */
    void stop();
    
    /**
     * @brief Check if monitoring is active
     */
    bool is_running() const;
    
    /**
     * @brief Register health check function
     */
    void register_health_check(const std::string& component, HealthCheckFunction check_func);
    
    /**
     * @brief Unregister health check function
     */
    void unregister_health_check(const std::string& component);
    
    /**
     * @brief Run health check for specific component
     */
    HealthCheckResult run_health_check(const std::string& component);
    
    /**
     * @brief Run all health checks
     */
    std::vector<HealthCheckResult> run_all_health_checks();
    
    /**
     * @brief Get overall health status
     */
    HealthStatus get_overall_health_status() const;
    
    /**
     * @brief Get latest health check results
     */
    std::vector<HealthCheckResult> get_latest_results() const;
    
    /**
     * @brief Get system metrics
     */
    SystemMetrics get_system_metrics() const;
    
    /**
     * @brief Get historical metrics
     */
    std::vector<SystemMetrics> get_historical_metrics(std::chrono::minutes duration) const;
    
    /**
     * @brief Set health event callback
     */
    void set_health_event_callback(HealthEventCallback callback);
    
    /**
     * @brief Get health summary
     */
    Json get_health_summary() const;
    
    /**
     * @brief Get detailed health report
     */
    Json get_detailed_health_report() const;
    
    /**
     * @brief Update configuration
     */
    void update_config(const HealthMonitorConfig& config);
    
    /**
     * @brief Get current configuration
     */
    const HealthMonitorConfig& get_config() const;

private:
    // Configuration
    HealthMonitorConfig config_;
    mutable std::mutex config_mutex_;
    
    // Health checks
    std::unordered_map<std::string, HealthCheckFunction> health_checks_;
    mutable std::shared_mutex health_checks_mutex_;
    
    // Results storage
    std::vector<HealthCheckResult> latest_results_;
    mutable std::mutex results_mutex_;
    
    // Metrics storage
    std::vector<SystemMetrics> metrics_history_;
    mutable std::mutex metrics_mutex_;
    static constexpr size_t MAX_METRICS_HISTORY = 1440; // 24 hours at 1-minute intervals
    
    // Monitoring threads
    std::atomic<bool> running_{false};
    std::unique_ptr<std::thread> health_check_thread_;
    std::unique_ptr<std::thread> metrics_thread_;
    
    // Event callback
    HealthEventCallback event_callback_;
    std::mutex event_callback_mutex_;
    
    // Logging
    std::shared_ptr<spdlog::logger> logger_;
    
    // Internal methods
    void health_check_loop();
    void metrics_collection_loop();
    
    SystemMetrics collect_system_metrics() const;
    void store_metrics(const SystemMetrics& metrics);
    void cleanup_old_metrics();
    
    HealthStatus evaluate_health_status(const std::vector<HealthCheckResult>& results) const;
    void notify_health_event(const HealthCheckResult& result);
    
    // System metrics collection
    double get_cpu_usage() const;
    uint64_t get_memory_usage() const;
    uint64_t get_total_memory() const;
    uint64_t get_disk_usage() const;
    uint64_t get_total_disk_space() const;
    
    // Platform-specific implementations
#ifdef _WIN32
    double get_cpu_usage_windows() const;
    uint64_t get_memory_usage_windows() const;
    uint64_t get_disk_usage_windows() const;
#else
    double get_cpu_usage_linux() const;
    uint64_t get_memory_usage_linux() const;
    uint64_t get_disk_usage_linux() const;
#endif
};

/**
 * @brief Built-in health check functions
 */
namespace BuiltinHealthChecks {

/**
 * @brief Basic connectivity health check
 */
HealthCheckFunction connectivity_check();

/**
 * @brief Database connectivity health check
 */
HealthCheckFunction database_check(const std::string& connection_string);

/**
 * @brief Service dependency health check
 */
HealthCheckFunction service_dependency_check(const std::string& service_url);

/**
 * @brief Memory usage health check
 */
HealthCheckFunction memory_usage_check(double warning_threshold = 80.0, double critical_threshold = 95.0);

/**
 * @brief Disk space health check
 */
HealthCheckFunction disk_space_check(const std::string& path, double warning_threshold = 85.0, double critical_threshold = 95.0);

} // namespace BuiltinHealthChecks

} // namespace QuantServices
