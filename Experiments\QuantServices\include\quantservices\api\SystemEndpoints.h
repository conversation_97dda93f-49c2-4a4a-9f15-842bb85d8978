/**
 * @file SystemEndpoints.h
 * @brief REST API endpoints for system management and monitoring
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include "../RestApiServer.h"
#include "../ServiceManager.h"
#include "../HealthMonitor.h"
#include "../ConfigManager.h"
#include <memory>

namespace QuantServices {

/**
 * @brief System API endpoints handler
 */
class SystemEndpoints {
public:
    /**
     * @brief Constructor
     */
    explicit SystemEndpoints(std::shared_ptr<ServiceManager> service_manager,
                            std::shared_ptr<HealthMonitor> health_monitor,
                            std::shared_ptr<ConfigManager> config_manager);
    
    /**
     * @brief Destructor
     */
    ~SystemEndpoints() = default;
    
    // Non-copyable, non-movable
    SystemEndpoints(const SystemEndpoints&) = delete;
    SystemEndpoints& operator=(const SystemEndpoints&) = delete;
    SystemEndpoints(SystemEndpoints&&) = delete;
    SystemEndpoints& operator=(SystemEndpoints&&) = delete;
    
    /**
     * @brief Register all System endpoints with the server
     */
    void register_endpoints(RestApiServer& server);

private:
    // Core services
    std::shared_ptr<ServiceManager> service_manager_;
    std::shared_ptr<HealthMonitor> health_monitor_;
    std::shared_ptr<ConfigManager> config_manager_;
    
    // System information endpoints
    ApiResponse get_system_info(const RequestContext& context);
    ApiResponse get_version_info(const RequestContext& context);
    ApiResponse get_build_info(const RequestContext& context);
    
    // Health and monitoring endpoints
    ApiResponse get_health_status(const RequestContext& context);
    ApiResponse get_health_check(const RequestContext& context);
    ApiResponse get_system_metrics(const RequestContext& context);
    ApiResponse get_historical_metrics(const RequestContext& context);
    ApiResponse get_performance_metrics(const RequestContext& context);
    
    // Service management endpoints
    ApiResponse get_services_status(const RequestContext& context);
    ApiResponse get_service_status(const RequestContext& context);
    ApiResponse start_service(const RequestContext& context);
    ApiResponse stop_service(const RequestContext& context);
    ApiResponse restart_service(const RequestContext& context);
    
    // Configuration management endpoints
    ApiResponse get_configuration(const RequestContext& context);
    ApiResponse update_configuration(const RequestContext& context);
    ApiResponse get_config_section(const RequestContext& context);
    ApiResponse update_config_section(const RequestContext& context);
    ApiResponse validate_configuration(const RequestContext& context);
    ApiResponse reload_configuration(const RequestContext& context);
    
    // Logging endpoints
    ApiResponse get_log_level(const RequestContext& context);
    ApiResponse set_log_level(const RequestContext& context);
    ApiResponse get_recent_logs(const RequestContext& context);
    
    // API documentation endpoints
    ApiResponse get_api_documentation(const RequestContext& context);
    ApiResponse get_openapi_spec(const RequestContext& context);
    ApiResponse get_endpoint_list(const RequestContext& context);
    
    // Server management endpoints
    ApiResponse shutdown_server(const RequestContext& context);
    ApiResponse restart_server(const RequestContext& context);
    ApiResponse get_server_stats(const RequestContext& context);
    
    // Diagnostic endpoints
    ApiResponse run_diagnostics(const RequestContext& context);
    ApiResponse get_diagnostic_report(const RequestContext& context);
    ApiResponse test_connectivity(const RequestContext& context);
    
    // Utility methods
    bool validate_service_name(const std::string& service_name) const;
    bool validate_log_level(const std::string& log_level) const;
    bool validate_config_section(const std::string& section) const;
    
    Json create_system_info() const;
    Json create_version_info() const;
    Json create_build_info() const;
    Json create_server_stats() const;
    
    std::string extract_service_name(const RequestContext& context) const;
    std::string extract_config_section(const RequestContext& context) const;
    
    // Error handling
    ApiResponse create_service_not_found_error(const std::string& service_name) const;
    ApiResponse create_config_validation_error(const std::string& message) const;
    ApiResponse create_permission_denied_error() const;
};

/**
 * @brief System endpoint registration helper
 */
namespace SystemEndpointHelper {

/**
 * @brief Register all System endpoints
 */
void register_all_endpoints(RestApiServer& server,
                           std::shared_ptr<ServiceManager> service_manager,
                           std::shared_ptr<HealthMonitor> health_monitor,
                           std::shared_ptr<ConfigManager> config_manager);

/**
 * @brief Get System API documentation
 */
Json get_api_documentation();

/**
 * @brief Get OpenAPI specification
 */
Json get_openapi_specification();

/**
 * @brief Get configuration schema
 */
Json get_configuration_schema();

} // namespace SystemEndpointHelper

} // namespace QuantServices
