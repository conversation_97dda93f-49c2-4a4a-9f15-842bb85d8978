/**
 * @file QuantServices.h
 * @brief Main header for QuantServices - Integrated DataHub and Trading Services
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

// Standard library includes
#include <memory>
#include <string>
#include <vector>
#include <chrono>
#include <future>
#include <functional>

// Third-party includes
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>
#include <boost/beast.hpp>
#include <boost/asio.hpp>

// Core components
#include "ServiceManager.h"
#include "RestApiServer.h"
#include "ConfigManager.h"
#include "HealthMonitor.h"

// API endpoints
#include "api/DataHubEndpoints.h"
#include "api/TradingEndpoints.h"
#include "api/SystemEndpoints.h"

/**
 * @brief Main namespace for QuantServices
 */
namespace QuantServices {

/**
 * @brief Version information
 */
constexpr const char* VERSION = "1.0.0";
constexpr int VERSION_MAJOR = 1;
constexpr int VERSION_MINOR = 0;
constexpr int VERSION_PATCH = 0;

/**
 * @brief Common type aliases
 */
using Json = nlohmann::json;
using TimePoint = std::chrono::system_clock::time_point;
using Duration = std::chrono::milliseconds;

/**
 * @brief Service status enumeration
 */
enum class ServiceStatus {
    Stopped,
    Starting,
    Running,
    Stopping,
    Error
};

/**
 * @brief Convert service status to string
 */
std::string to_string(ServiceStatus status);

/**
 * @brief Service configuration structure
 */
struct ServiceConfig {
    // Server configuration
    std::string server_host{"0.0.0.0"};
    uint16_t server_port{8080};
    size_t worker_threads{4};
    
    // DataHub configuration
    bool enable_datahub{true};
    std::string datahub_config_file{"config/datahub.json"};
    
    // Trading configuration
    bool enable_trading{true};
    std::string trading_config_file{"config/trading.json"};
    
    // Logging configuration
    std::string log_level{"info"};
    std::string log_file{"logs/quantservices.log"};
    bool log_to_console{true};
    
    // Health monitoring
    Duration health_check_interval{std::chrono::seconds(30)};
    Duration request_timeout{std::chrono::seconds(30)};
    
    // API configuration
    bool enable_cors{true};
    std::vector<std::string> allowed_origins{"*"};
    size_t max_request_size{1024 * 1024}; // 1MB
    
    // Security (basic)
    bool enable_auth{false};
    std::string api_key{};
    
    // Convert to JSON
    Json to_json() const;
    
    // Load from JSON
    static ServiceConfig from_json(const Json& j);
};

/**
 * @brief Main QuantServices application class
 */
class QuantServicesApp {
public:
    /**
     * @brief Constructor
     */
    explicit QuantServicesApp(const ServiceConfig& config = ServiceConfig{});
    
    /**
     * @brief Destructor
     */
    ~QuantServicesApp();
    
    // Non-copyable, non-movable
    QuantServicesApp(const QuantServicesApp&) = delete;
    QuantServicesApp& operator=(const QuantServicesApp&) = delete;
    QuantServicesApp(QuantServicesApp&&) = delete;
    QuantServicesApp& operator=(QuantServicesApp&&) = delete;
    
    /**
     * @brief Initialize the application
     */
    bool initialize();
    
    /**
     * @brief Start all services
     */
    bool start();
    
    /**
     * @brief Stop all services
     */
    void stop();
    
    /**
     * @brief Run the application (blocking)
     */
    int run();
    
    /**
     * @brief Get current status
     */
    ServiceStatus get_status() const;
    
    /**
     * @brief Get service manager
     */
    std::shared_ptr<ServiceManager> get_service_manager() const;
    
    /**
     * @brief Get REST API server
     */
    std::shared_ptr<RestApiServer> get_api_server() const;
    
    /**
     * @brief Get configuration
     */
    const ServiceConfig& get_config() const;
    
    /**
     * @brief Update configuration
     */
    bool update_config(const ServiceConfig& new_config);
    
    /**
     * @brief Get health status
     */
    Json get_health_status() const;
    
    /**
     * @brief Get system metrics
     */
    Json get_system_metrics() const;

private:
    // Configuration
    ServiceConfig config_;
    
    // Core components
    std::shared_ptr<ConfigManager> config_manager_;
    std::shared_ptr<ServiceManager> service_manager_;
    std::shared_ptr<RestApiServer> api_server_;
    std::shared_ptr<HealthMonitor> health_monitor_;
    
    // Status
    mutable std::mutex status_mutex_;
    ServiceStatus status_{ServiceStatus::Stopped};
    
    // Logging
    std::shared_ptr<spdlog::logger> logger_;
    
    // Internal methods
    bool setup_logging();
    bool setup_signal_handlers();
    void handle_shutdown_signal();
    void set_status(ServiceStatus status);
};

/**
 * @brief Factory functions
 */
namespace Factory {

/**
 * @brief Create default QuantServices application
 */
std::unique_ptr<QuantServicesApp> create_default_app(uint16_t port = 8080);

/**
 * @brief Create QuantServices application from config file
 */
std::unique_ptr<QuantServicesApp> create_app_from_config(const std::string& config_file);

/**
 * @brief Create QuantServices application with custom config
 */
std::unique_ptr<QuantServicesApp> create_app_with_config(const ServiceConfig& config);

} // namespace Factory

/**
 * @brief Utility functions
 */
namespace Utils {

/**
 * @brief Get current timestamp as ISO string
 */
std::string get_current_timestamp();

/**
 * @brief Parse timestamp from ISO string
 */
TimePoint parse_timestamp(const std::string& timestamp);

/**
 * @brief Generate UUID string
 */
std::string generate_uuid();

/**
 * @brief Validate JSON schema
 */
bool validate_json_schema(const Json& data, const Json& schema);

/**
 * @brief Create error response JSON
 */
Json create_error_response(const std::string& error, const std::string& details = "");

/**
 * @brief Create success response JSON
 */
Json create_success_response(const Json& data = Json::object());

} // namespace Utils

} // namespace QuantServices
