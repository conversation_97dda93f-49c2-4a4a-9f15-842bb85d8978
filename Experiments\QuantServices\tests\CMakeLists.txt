# Tests CMakeLists.txt for QuantServices

# Find Google Test
find_package(GTest QUIET)

if(NOT GTest_FOUND)
    # Fetch Google Test if not found
    include(FetchContent)
    FetchContent_Declare(
        googletest
        GIT_REPOSITORY https://github.com/google/googletest.git
        GIT_TAG release-1.12.1
    )
    
    # For Windows: Prevent overriding the parent project's compiler/linker settings
    set(gtest_force_shared_crt ON CACHE BOOL "" FORCE)
    FetchContent_MakeAvailable(googletest)
endif()

# Test sources
set(TEST_SOURCES
    test_integration.cpp
    test_config_manager.cpp
    test_health_monitor.cpp
    test_rest_api_server.cpp
    test_service_manager.cpp
)

# Create test executable
add_executable(quantservices_tests ${TEST_SOURCES})

# Link libraries
target_link_libraries(quantservices_tests
    PRIVATE
        ${PROJECT_NAME}
        gtest_main
        gtest
        Threads::Threads
        Boost::system
        Boost::thread
        Boost::filesystem
        Boost::json
        spdlog::spdlog
        nlohmann_json::nlohmann_json
        fmt::fmt
)

# Set output directory
set_target_properties(quantservices_tests PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests
)

# Include directories
target_include_directories(quantservices_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/include
        ${CMAKE_SOURCE_DIR}/src
)

# Discover tests
include(GoogleTest)
gtest_discover_tests(quantservices_tests)

# Copy test configuration files
configure_file(${CMAKE_SOURCE_DIR}/config/default.json 
               ${CMAKE_BINARY_DIR}/tests/config/test.json 
               COPYONLY)

# Create test directory structure
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/tests/logs)
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/tests/data)

# Add custom test target
add_custom_target(run_tests
    COMMAND ${CMAKE_BINARY_DIR}/tests/quantservices_tests
    DEPENDS quantservices_tests
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/tests
    COMMENT "Running QuantServices tests"
)
