#include <catch2/catch_test_macros.hpp>
#include "data/SqliteRepository.h"
#include "core/MarketData.h"
#include <filesystem>

using namespace DataHub::Core;
using namespace DataHub::Data;

TEST_CASE("SqliteRepository - Basic functionality", "[sqlite]") {
    // Use a temporary database file
    std::string db_path = "test_datahub.db";

    // Clean up any existing test database
    if (std::filesystem::exists(db_path)) {
        std::filesystem::remove(db_path);
    }

    SqliteRepository repo(db_path);

    SECTION("Connection management") {
        REQUIRE_FALSE(repo.is_connected());

        auto connect_result = repo.connect();
        REQUIRE(connect_result.is_success());
        REQUIRE(repo.is_connected());

        auto health_result = repo.health_check();
        REQUIRE(health_result.is_success());
        REQUIRE(health_result.value() == true);

        auto disconnect_result = repo.disconnect();
        REQUIRE(disconnect_result.is_success());
        REQUIRE_FALSE(repo.is_connected());
    }

    // Clean up test database
    if (std::filesystem::exists(db_path)) {
        std::filesystem::remove(db_path);
    }
}
