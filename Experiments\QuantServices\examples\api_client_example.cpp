/**
 * @file api_client_example.cpp
 * @brief API client example for QuantServices
 * <AUTHOR> Team
 * @date 2024
 */

#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <curl/curl.h>
#include <nlohmann/json.hpp>

using Json = nlohmann::json;

// Simple HTTP client using libcurl
class SimpleHttpClient {
public:
    SimpleHttpClient() {
        curl_global_init(CURL_GLOBAL_DEFAULT);
        curl_ = curl_easy_init();
    }
    
    ~SimpleHttpClient() {
        if (curl_) {
            curl_easy_cleanup(curl_);
        }
        curl_global_cleanup();
    }
    
    std::string get(const std::string& url) {
        if (!curl_) return "";
        
        response_data_.clear();
        
        curl_easy_setopt(curl_, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl_, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl_, CURLOPT_WRITEDATA, this);
        curl_easy_setopt(curl_, CURLOPT_TIMEOUT, 30L);
        
        CURLcode res = curl_easy_perform(curl_);
        if (res != CURLE_OK) {
            std::cerr << "curl_easy_perform() failed: " << curl_easy_strerror(res) << std::endl;
            return "";
        }
        
        return response_data_;
    }
    
    std::string post(const std::string& url, const std::string& data) {
        if (!curl_) return "";
        
        response_data_.clear();
        
        curl_easy_setopt(curl_, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl_, CURLOPT_POSTFIELDS, data.c_str());
        curl_easy_setopt(curl_, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl_, CURLOPT_WRITEDATA, this);
        curl_easy_setopt(curl_, CURLOPT_TIMEOUT, 30L);
        
        struct curl_slist* headers = nullptr;
        headers = curl_slist_append(headers, "Content-Type: application/json");
        curl_easy_setopt(curl_, CURLOPT_HTTPHEADER, headers);
        
        CURLcode res = curl_easy_perform(curl_);
        
        curl_slist_free_all(headers);
        
        if (res != CURLE_OK) {
            std::cerr << "curl_easy_perform() failed: " << curl_easy_strerror(res) << std::endl;
            return "";
        }
        
        return response_data_;
    }

private:
    CURL* curl_;
    std::string response_data_;
    
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, SimpleHttpClient* client) {
        size_t total_size = size * nmemb;
        client->response_data_.append(static_cast<char*>(contents), total_size);
        return total_size;
    }
};

void test_system_endpoints(SimpleHttpClient& client, const std::string& base_url) {
    std::cout << "\n=== Testing System Endpoints ===" << std::endl;
    
    // Test system info
    std::cout << "Getting system info..." << std::endl;
    auto response = client.get(base_url + "/system/info");
    if (!response.empty()) {
        try {
            auto json_response = Json::parse(response);
            std::cout << "System Info: " << json_response.dump(2) << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Raw response: " << response << std::endl;
        }
    }
    
    // Test health status
    std::cout << "\nGetting health status..." << std::endl;
    response = client.get(base_url + "/system/health");
    if (!response.empty()) {
        try {
            auto json_response = Json::parse(response);
            std::cout << "Health Status: " << json_response.dump(2) << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Raw response: " << response << std::endl;
        }
    }
    
    // Test metrics
    std::cout << "\nGetting system metrics..." << std::endl;
    response = client.get(base_url + "/system/metrics");
    if (!response.empty()) {
        try {
            auto json_response = Json::parse(response);
            std::cout << "System Metrics: " << json_response.dump(2) << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Raw response: " << response << std::endl;
        }
    }
}

void test_datahub_endpoints(SimpleHttpClient& client, const std::string& base_url) {
    std::cout << "\n=== Testing DataHub Endpoints ===" << std::endl;
    
    // Test DataHub status
    std::cout << "Getting DataHub status..." << std::endl;
    auto response = client.get(base_url + "/datahub/status");
    if (!response.empty()) {
        try {
            auto json_response = Json::parse(response);
            std::cout << "DataHub Status: " << json_response.dump(2) << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Raw response: " << response << std::endl;
        }
    }
    
    // Test quote data
    std::cout << "\nGetting quote for AAPL..." << std::endl;
    response = client.get(base_url + "/datahub/quote/AAPL");
    if (!response.empty()) {
        try {
            auto json_response = Json::parse(response);
            std::cout << "AAPL Quote: " << json_response.dump(2) << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Raw response: " << response << std::endl;
        }
    }
    
    // Test multiple quotes
    std::cout << "\nGetting quotes for multiple symbols..." << std::endl;
    response = client.get(base_url + "/datahub/quotes?symbols=AAPL,GOOGL,MSFT");
    if (!response.empty()) {
        try {
            auto json_response = Json::parse(response);
            std::cout << "Multiple Quotes: " << json_response.dump(2) << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Raw response: " << response << std::endl;
        }
    }
    
    // Test historical data
    std::cout << "\nGetting historical bars for AAPL..." << std::endl;
    response = client.get(base_url + "/datahub/bars/AAPL?bar_size=1m&limit=10");
    if (!response.empty()) {
        try {
            auto json_response = Json::parse(response);
            std::cout << "AAPL Historical Bars: " << json_response.dump(2) << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Raw response: " << response << std::endl;
        }
    }
}

void test_trading_endpoints(SimpleHttpClient& client, const std::string& base_url) {
    std::cout << "\n=== Testing Trading Endpoints ===" << std::endl;
    
    // Test Trading status
    std::cout << "Getting Trading status..." << std::endl;
    auto response = client.get(base_url + "/trading/status");
    if (!response.empty()) {
        try {
            auto json_response = Json::parse(response);
            std::cout << "Trading Status: " << json_response.dump(2) << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Raw response: " << response << std::endl;
        }
    }
    
    // Test strategies
    std::cout << "\nGetting strategies..." << std::endl;
    response = client.get(base_url + "/trading/strategies");
    if (!response.empty()) {
        try {
            auto json_response = Json::parse(response);
            std::cout << "Strategies: " << json_response.dump(2) << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Raw response: " << response << std::endl;
        }
    }
    
    // Test create order
    std::cout << "\nCreating a test order..." << std::endl;
    Json order_request = {
        {"symbol", "AAPL"},
        {"side", "buy"},
        {"quantity", 100},
        {"order_type", "market"},
        {"strategy_id", "test_strategy"}
    };
    
    response = client.post(base_url + "/trading/orders", order_request.dump());
    if (!response.empty()) {
        try {
            auto json_response = Json::parse(response);
            std::cout << "Order Creation Response: " << json_response.dump(2) << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Raw response: " << response << std::endl;
        }
    }
    
    // Test get orders
    std::cout << "\nGetting orders..." << std::endl;
    response = client.get(base_url + "/trading/orders");
    if (!response.empty()) {
        try {
            auto json_response = Json::parse(response);
            std::cout << "Orders: " << json_response.dump(2) << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Raw response: " << response << std::endl;
        }
    }
}

int main() {
    std::cout << "=== QuantServices API Client Example ===" << std::endl;
    
    // Configuration
    std::string host = "127.0.0.1";
    int port = 8080;
    std::string base_url = "http://" + host + ":" + std::to_string(port) + "/api/v1";
    
    std::cout << "Testing API at: " << base_url << std::endl;
    std::cout << "Make sure QuantServices is running before executing this example." << std::endl;
    std::cout << "Press Enter to continue...";
    std::cin.get();
    
    try {
        SimpleHttpClient client;
        
        // Test different endpoint categories
        test_system_endpoints(client, base_url);
        test_datahub_endpoints(client, base_url);
        test_trading_endpoints(client, base_url);
        
        std::cout << "\n=== API Client Example Completed ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
