# ????????
cmake_minimum_required(VERSION 3.20)

# Test source files
set(TEST_SOURCES
    test_core_types.cpp
    test_market_data.cpp
    test_security_info.cpp
    test_data_aggregation.cpp
    test_performance.cpp
    # test_sqlite_repository.cpp  # Temporarily disabled due to linking issues
)

# ???????????????
add_executable(datahub_tests
    ${TEST_SOURCES}
)

# ?????
target_link_libraries(datahub_tests
    PRIVATE
        datahub_core
        Catch2::Catch2WithMain
)

# ??????
target_include_directories(datahub_tests
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/../include
)

# ???????
target_compile_features(datahub_tests PRIVATE cxx_std_20)

# ???????
include(CTest)
include(Catch)

catch_discover_tests(datahub_tests)

# Individual test targets
add_test(NAME core_types_test COMMAND datahub_tests "[types]")
add_test(NAME market_data_test COMMAND datahub_tests "[marketdata]")
add_test(NAME security_info_test COMMAND datahub_tests "[security]")
add_test(NAME data_aggregation_test COMMAND datahub_tests "[aggregation]")
add_test(NAME performance_test COMMAND datahub_tests "[performance]")
# add_test(NAME sqlite_repository_test COMMAND datahub_tests "[sqlite]")  # Temporarily disabled

# ????????? (???)
if(CMAKE_BUILD_TYPE STREQUAL "Debug" AND CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    option(ENABLE_COVERAGE "Enable coverage reporting" OFF)
    
    if(ENABLE_COVERAGE)
        target_compile_options(datahub_tests PRIVATE --coverage)
        target_link_options(datahub_tests PRIVATE --coverage)
        
        # ????????????
        find_program(GCOV_PATH gcov)
        find_program(LCOV_PATH lcov)
        find_program(GENHTML_PATH genhtml)
        
        if(GCOV_PATH AND LCOV_PATH AND GENHTML_PATH)
            add_custom_target(coverage
                COMMAND ${LCOV_PATH} --directory . --capture --output-file coverage.info
                COMMAND ${LCOV_PATH} --remove coverage.info '/usr/*' --output-file coverage.info
                COMMAND ${LCOV_PATH} --list coverage.info
                COMMAND ${GENHTML_PATH} -o coverage coverage.info
                WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
                COMMENT "Generating code coverage report"
            )
        endif()
    endif()
endif()

# ????? (???)
find_program(VALGRIND_PATH valgrind)
if(VALGRIND_PATH)
    add_custom_target(memcheck
        COMMAND ${VALGRIND_PATH} --tool=memcheck --leak-check=full --show-leak-kinds=all 
                --track-origins=yes --verbose $<TARGET_FILE:datahub_tests>
        DEPENDS datahub_tests
        COMMENT "Running memory check with Valgrind"
    )
endif()

# ??????? (???)
option(ENABLE_BENCHMARKS "Enable benchmark tests" OFF)
if(ENABLE_BENCHMARKS)
    # ?????????????????
    add_executable(datahub_benchmarks
        benchmark_market_data.cpp
    )
    
    target_link_libraries(datahub_benchmarks
        PRIVATE
            datahub_core
    )
endif()
