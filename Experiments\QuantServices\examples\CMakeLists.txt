# Examples CMakeLists.txt for QuantServices

# Basic usage example
add_executable(basic_usage basic_usage.cpp)
target_link_libraries(basic_usage
    PRIVATE
        ${PROJECT_NAME}
        Threads::Threads
        Boost::system
        Boost::thread
        Boost::filesystem
        Boost::json
        spdlog::spdlog
        nlohmann_json::nlohmann_json
        fmt::fmt
)

# Set output directory
set_target_properties(basic_usage PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/examples
)

# API client example (requires libcurl)
find_package(PkgConfig)
if(PkgConfig_FOUND)
    pkg_check_modules(CURL QUIET libcurl)
endif()

if(CURL_FOUND)
    add_executable(api_client_example api_client_example.cpp)
    target_link_libraries(api_client_example
        PRIVATE
            nlohmann_json::nlohmann_json
            ${CURL_LIBRARIES}
    )
    target_include_directories(api_client_example PRIVATE ${CURL_INCLUDE_DIRS})
    target_compile_options(api_client_example PRIVATE ${CURL_CFLAGS_OTHER})
    
    set_target_properties(api_client_example PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/examples
    )
    
    message(STATUS "API client example will be built with libcurl")
else()
    message(STATUS "libcurl not found, API client example will be skipped")
endif()

# Copy configuration files to examples directory
configure_file(${CMAKE_SOURCE_DIR}/config/default.json 
               ${CMAKE_BINARY_DIR}/examples/config/default.json 
               COPYONLY)

# Create examples directory structure
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/examples/logs)
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/examples/data)
