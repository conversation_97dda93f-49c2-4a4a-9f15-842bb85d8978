/**
 * @file main.cpp
 * @brief Main entry point for QuantServices application
 * <AUTHOR> Team
 * @date 2024
 */

#include "quantservices/QuantServices.h"
#include <iostream>
#include <csignal>
#include <memory>
#include <filesystem>

using namespace QuantServices;

// Global application instance for signal handling
std::unique_ptr<QuantServicesApp> g_app;

// Signal handler for graceful shutdown
void signal_handler(int signal) {
    std::cout << "\nReceived signal " << signal << ", shutting down gracefully..." << std::endl;
    if (g_app) {
        g_app->stop();
    }
}

// Setup signal handlers
void setup_signal_handlers() {
    std::signal(SIGINT, signal_handler);   // Ctrl+C
    std::signal(SIGTERM, signal_handler);  // Termination request
#ifndef _WIN32
    std::signal(SIGHUP, signal_handler);   // Hangup (Unix only)
#endif
}

// Print usage information
void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [options]\n"
              << "\n"
              << "Options:\n"
              << "  -h, --help              Show this help message\n"
              << "  -v, --version           Show version information\n"
              << "  -c, --config <file>     Configuration file path (default: config/default.json)\n"
              << "  -p, --port <port>       Server port (default: 8080)\n"
              << "  -H, --host <host>       Server host (default: 0.0.0.0)\n"
              << "  -l, --log-level <level> Log level (debug, info, warn, error) (default: info)\n"
              << "  -d, --daemon            Run as daemon (background)\n"
              << "  --no-datahub            Disable DataHub service\n"
              << "  --no-trading            Disable Trading service\n"
              << "\n"
              << "Examples:\n"
              << "  " << program_name << "                           # Run with default settings\n"
              << "  " << program_name << " -c config/production.json # Run with production config\n"
              << "  " << program_name << " -p 9090 -l debug         # Run on port 9090 with debug logging\n"
              << std::endl;
}

// Print version information
void print_version() {
    std::cout << "QuantServices " << VERSION << "\n"
              << "Build: " << __DATE__ << " " << __TIME__ << "\n"
              << "C++ Standard: " << __cplusplus << "\n"
              << "\n"
              << "Components:\n"
              << "  DataHub Integration: "
#ifdef DATAHUB_AVAILABLE
              << "Available"
#else
              << "Not Available"
#endif
              << "\n"
              << "  Trading Integration: "
#ifdef TRADING_AVAILABLE
              << "Available"
#else
              << "Not Available"
#endif
              << "\n"
              << "\n"
              << "Copyright (c) 2024 RoboQuant Team\n"
              << std::endl;
}

// Parse command line arguments
ServiceConfig parse_arguments(int argc, char* argv[]) {
    ServiceConfig config;
    std::string config_file = "config/default.json";
    
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            print_usage(argv[0]);
            std::exit(0);
        } else if (arg == "-v" || arg == "--version") {
            print_version();
            std::exit(0);
        } else if (arg == "-c" || arg == "--config") {
            if (i + 1 < argc) {
                config_file = argv[++i];
            } else {
                std::cerr << "Error: --config requires a file path" << std::endl;
                std::exit(1);
            }
        } else if (arg == "-p" || arg == "--port") {
            if (i + 1 < argc) {
                try {
                    config.server_port = static_cast<uint16_t>(std::stoi(argv[++i]));
                } catch (const std::exception& e) {
                    std::cerr << "Error: Invalid port number: " << argv[i] << std::endl;
                    std::exit(1);
                }
            } else {
                std::cerr << "Error: --port requires a port number" << std::endl;
                std::exit(1);
            }
        } else if (arg == "-H" || arg == "--host") {
            if (i + 1 < argc) {
                config.server_host = argv[++i];
            } else {
                std::cerr << "Error: --host requires a host address" << std::endl;
                std::exit(1);
            }
        } else if (arg == "-l" || arg == "--log-level") {
            if (i + 1 < argc) {
                config.log_level = argv[++i];
            } else {
                std::cerr << "Error: --log-level requires a level (debug, info, warn, error)" << std::endl;
                std::exit(1);
            }
        } else if (arg == "-d" || arg == "--daemon") {
            // Daemon mode - would need platform-specific implementation
            std::cout << "Note: Daemon mode not implemented yet" << std::endl;
        } else if (arg == "--no-datahub") {
            config.enable_datahub = false;
        } else if (arg == "--no-trading") {
            config.enable_trading = false;
        } else {
            std::cerr << "Error: Unknown option: " << arg << std::endl;
            print_usage(argv[0]);
            std::exit(1);
        }
    }
    
    // Load configuration from file if it exists
    if (std::filesystem::exists(config_file)) {
        try {
            std::ifstream file(config_file);
            Json config_json;
            file >> config_json;
            
            // Override with file configuration
            auto file_config = ServiceConfig::from_json(config_json);
            
            // Command line arguments take precedence
            if (config.server_port == 8080 && file_config.server_port != 8080) {
                config.server_port = file_config.server_port;
            }
            if (config.server_host == "0.0.0.0" && file_config.server_host != "0.0.0.0") {
                config.server_host = file_config.server_host;
            }
            if (config.log_level == "info" && file_config.log_level != "info") {
                config.log_level = file_config.log_level;
            }
            
            // Copy other settings from file
            config.worker_threads = file_config.worker_threads;
            config.datahub_config_file = file_config.datahub_config_file;
            config.trading_config_file = file_config.trading_config_file;
            config.log_file = file_config.log_file;
            config.log_to_console = file_config.log_to_console;
            config.health_check_interval = file_config.health_check_interval;
            config.request_timeout = file_config.request_timeout;
            config.enable_cors = file_config.enable_cors;
            config.allowed_origins = file_config.allowed_origins;
            config.max_request_size = file_config.max_request_size;
            config.enable_auth = file_config.enable_auth;
            config.api_key = file_config.api_key;
            
        } catch (const std::exception& e) {
            std::cerr << "Warning: Failed to load config file " << config_file 
                      << ": " << e.what() << std::endl;
            std::cerr << "Using default configuration" << std::endl;
        }
    } else {
        std::cout << "Config file " << config_file << " not found, using default configuration" << std::endl;
    }
    
    return config;
}

// Main function
int main(int argc, char* argv[]) {
    try {
        // Parse command line arguments
        auto config = parse_arguments(argc, argv);
        
        // Setup signal handlers
        setup_signal_handlers();
        
        // Print startup banner
        std::cout << "Starting QuantServices " << VERSION << std::endl;
        std::cout << "Server: " << config.server_host << ":" << config.server_port << std::endl;
        std::cout << "DataHub: " << (config.enable_datahub ? "Enabled" : "Disabled") << std::endl;
        std::cout << "Trading: " << (config.enable_trading ? "Enabled" : "Disabled") << std::endl;
        std::cout << "Log Level: " << config.log_level << std::endl;
        std::cout << std::endl;
        
        // Create and initialize application
        g_app = std::make_unique<QuantServicesApp>(config);
        
        if (!g_app->initialize()) {
            std::cerr << "Failed to initialize QuantServices application" << std::endl;
            return 1;
        }
        
        if (!g_app->start()) {
            std::cerr << "Failed to start QuantServices application" << std::endl;
            return 1;
        }
        
        std::cout << "QuantServices started successfully!" << std::endl;
        std::cout << "API available at: http://" << config.server_host << ":" << config.server_port << "/api/v1" << std::endl;
        std::cout << "Press Ctrl+C to stop..." << std::endl;
        
        // Run the application (blocking)
        int exit_code = g_app->run();
        
        std::cout << "QuantServices stopped" << std::endl;
        return exit_code;
        
    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown fatal error occurred" << std::endl;
        return 1;
    }
}
