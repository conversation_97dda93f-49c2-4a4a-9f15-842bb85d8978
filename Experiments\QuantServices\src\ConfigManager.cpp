/**
 * @file ConfigManager.cpp
 * @brief Implementation of configuration management system
 * <AUTHOR> Team
 * @date 2024
 */

#include "quantservices/ConfigManager.h"
#include <fstream>
#include <filesystem>
#include <cstdlib>
#include <algorithm>
#include <spdlog/spdlog.h>

// For environment variables
extern char** environ;

namespace QuantServices {

ConfigManager::ConfigManager() 
    : logger_(spdlog::get("quantservices") ? spdlog::get("quantservices") : spdlog::default_logger()) {
    logger_->debug("ConfigManager initialized");
}

ConfigManager::~ConfigManager() {
    disable_file_watching();
    logger_->debug("ConfigManager destroyed");
}

bool ConfigManager::load_from_file(const std::string& file_path) {
    try {
        if (!std::filesystem::exists(file_path)) {
            logger_->error("Configuration file does not exist: {}", file_path);
            return false;
        }
        
        std::ifstream file(file_path);
        if (!file.is_open()) {
            logger_->error("Failed to open configuration file: {}", file_path);
            return false;
        }
        
        Json new_config;
        file >> new_config;
        
        {
            std::unique_lock lock(config_mutex_);
            config_ = std::move(new_config);
        }
        
        logger_->info("Configuration loaded from file: {}", file_path);
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to load configuration from file {}: {}", file_path, e.what());
        return false;
    }
}

bool ConfigManager::load_from_json(const Json& config) {
    try {
        {
            std::unique_lock lock(config_mutex_);
            config_ = config;
        }
        
        logger_->info("Configuration loaded from JSON");
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to load configuration from JSON: {}", e.what());
        return false;
    }
}

void ConfigManager::load_from_environment(const std::string& prefix) {
    try {
        Json env_config = Json::object();
        
        // Get all environment variables with the specified prefix
        for (char** env = environ; *env != nullptr; ++env) {
            std::string env_var(*env);
            auto eq_pos = env_var.find('=');
            if (eq_pos != std::string::npos) {
                std::string key = env_var.substr(0, eq_pos);
                std::string value = env_var.substr(eq_pos + 1);
                
                if (key.starts_with(prefix)) {
                    // Remove prefix and convert to lowercase
                    std::string config_key = key.substr(prefix.length());
                    std::transform(config_key.begin(), config_key.end(), config_key.begin(), ::tolower);
                    
                    // Replace underscores with dots for nested keys
                    std::replace(config_key.begin(), config_key.end(), '_', '.');
                    
                    // Try to parse as JSON, otherwise treat as string
                    try {
                        Json parsed_value = Json::parse(value);
                        set_nested_value(config_key, parsed_value);
                    } catch (const std::exception&) {
                        // Not valid JSON, treat as string
                        set_nested_value(config_key, value);
                    }
                }
            }
        }
        
        logger_->info("Environment variables loaded with prefix: {}", prefix);
        
    } catch (const std::exception& e) {
        logger_->error("Failed to load environment variables: {}", e.what());
    }
}

bool ConfigManager::save_to_file(const std::string& file_path) const {
    try {
        // Create directory if it doesn't exist
        auto dir = std::filesystem::path(file_path).parent_path();
        if (!dir.empty() && !std::filesystem::exists(dir)) {
            std::filesystem::create_directories(dir);
        }
        
        std::ofstream file(file_path);
        if (!file.is_open()) {
            logger_->error("Failed to open file for writing: {}", file_path);
            return false;
        }
        
        {
            std::shared_lock lock(config_mutex_);
            file << config_.dump(2); // Pretty print with 2-space indentation
        }
        
        logger_->info("Configuration saved to file: {}", file_path);
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to save configuration to file {}: {}", file_path, e.what());
        return false;
    }
}

bool ConfigManager::has(const std::string& key) const {
    std::shared_lock lock(config_mutex_);
    try {
        Json value = get_nested_value(key);
        return !value.is_null();
    } catch (const std::exception&) {
        return false;
    }
}

bool ConfigManager::remove(const std::string& key) {
    Json old_value;
    
    {
        std::unique_lock lock(config_mutex_);
        old_value = get_nested_value(key);
        if (old_value.is_null()) {
            return false; // Key doesn't exist
        }
        
        if (!remove_nested_value(key)) {
            return false;
        }
    }
    
    notify_change(key, old_value, Json{});
    return true;
}

Json ConfigManager::get_all() const {
    std::shared_lock lock(config_mutex_);
    return config_;
}

Json ConfigManager::get_section(const std::string& section) const {
    std::shared_lock lock(config_mutex_);
    try {
        return get_nested_value(section);
    } catch (const std::exception&) {
        return Json::object();
    }
}

void ConfigManager::set_section(const std::string& section, const Json& config) {
    Json old_value;
    
    {
        std::unique_lock lock(config_mutex_);
        old_value = get_nested_value(section);
        set_nested_value(section, config);
    }
    
    notify_change(section, old_value, config);
}

void ConfigManager::register_change_callback(const std::string& key, ConfigChangeCallback callback) {
    std::lock_guard lock(callbacks_mutex_);
    change_callbacks_[key] = std::move(callback);
    logger_->debug("Registered change callback for key: {}", key);
}

void ConfigManager::unregister_change_callback(const std::string& key) {
    std::lock_guard lock(callbacks_mutex_);
    change_callbacks_.erase(key);
    logger_->debug("Unregistered change callback for key: {}", key);
}

void ConfigManager::enable_file_watching(const std::string& file_path) {
    disable_file_watching(); // Stop any existing watcher
    
    if (!std::filesystem::exists(file_path)) {
        logger_->warn("Cannot watch non-existent file: {}", file_path);
        return;
    }
    
    watched_file_ = file_path;
    last_write_time_ = std::filesystem::last_write_time(file_path);
    file_watching_active_.store(true);
    
    file_watcher_thread_ = std::make_unique<std::thread>(&ConfigManager::file_watcher_loop, this);
    logger_->info("File watching enabled for: {}", file_path);
}

void ConfigManager::disable_file_watching() {
    if (file_watching_active_.load()) {
        file_watching_active_.store(false);
        
        if (file_watcher_thread_ && file_watcher_thread_->joinable()) {
            file_watcher_thread_->join();
        }
        file_watcher_thread_.reset();
        
        logger_->info("File watching disabled");
    }
}

bool ConfigManager::validate_schema(const Json& schema) const {
    // Basic JSON schema validation
    // This is a simplified implementation - a full JSON schema validator would be more comprehensive
    try {
        std::shared_lock lock(config_mutex_);
        
        if (!schema.contains("type") || schema["type"] != "object") {
            return false;
        }
        
        if (schema.contains("required") && schema["required"].is_array()) {
            for (const auto& required_key : schema["required"]) {
                if (!config_.contains(required_key.get<std::string>())) {
                    return false;
                }
            }
        }
        
        if (schema.contains("properties") && schema["properties"].is_object()) {
            for (const auto& [key, prop_schema] : schema["properties"].items()) {
                if (config_.contains(key)) {
                    // Basic type checking
                    if (prop_schema.contains("type")) {
                        std::string expected_type = prop_schema["type"];
                        const auto& value = config_[key];
                        
                        if (expected_type == "string" && !value.is_string()) return false;
                        if (expected_type == "number" && !value.is_number()) return false;
                        if (expected_type == "boolean" && !value.is_boolean()) return false;
                        if (expected_type == "array" && !value.is_array()) return false;
                        if (expected_type == "object" && !value.is_object()) return false;
                    }
                }
            }
        }
        
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Schema validation error: {}", e.what());
        return false;
    }
}

std::vector<std::string> ConfigManager::get_validation_errors(const Json& schema) const {
    std::vector<std::string> errors;
    
    try {
        std::shared_lock lock(config_mutex_);
        
        if (schema.contains("required") && schema["required"].is_array()) {
            for (const auto& required_key : schema["required"]) {
                std::string key = required_key.get<std::string>();
                if (!config_.contains(key)) {
                    errors.push_back("Missing required field: " + key);
                }
            }
        }
        
        if (schema.contains("properties") && schema["properties"].is_object()) {
            for (const auto& [key, prop_schema] : schema["properties"].items()) {
                if (config_.contains(key)) {
                    if (prop_schema.contains("type")) {
                        std::string expected_type = prop_schema["type"];
                        const auto& value = config_[key];
                        
                        bool type_valid = true;
                        if (expected_type == "string" && !value.is_string()) type_valid = false;
                        if (expected_type == "number" && !value.is_number()) type_valid = false;
                        if (expected_type == "boolean" && !value.is_boolean()) type_valid = false;
                        if (expected_type == "array" && !value.is_array()) type_valid = false;
                        if (expected_type == "object" && !value.is_object()) type_valid = false;
                        
                        if (!type_valid) {
                            errors.push_back("Invalid type for field '" + key + "': expected " + expected_type);
                        }
                    }
                }
            }
        }
        
    } catch (const std::exception& e) {
        errors.push_back("Schema validation exception: " + std::string(e.what()));
    }
    
    return errors;
}

void ConfigManager::merge(const Json& other_config) {
    std::unique_lock lock(config_mutex_);
    
    // Deep merge JSON objects
    std::function<void(Json&, const Json&)> deep_merge = [&](Json& target, const Json& source) {
        if (source.is_object() && target.is_object()) {
            for (const auto& [key, value] : source.items()) {
                if (target.contains(key) && target[key].is_object() && value.is_object()) {
                    deep_merge(target[key], value);
                } else {
                    target[key] = value;
                }
            }
        } else {
            target = source;
        }
    };
    
    deep_merge(config_, other_config);
    logger_->info("Configuration merged");
}

std::string ConfigManager::to_string(int indent) const {
    std::shared_lock lock(config_mutex_);
    return config_.dump(indent);
}

// Private methods implementation

Json ConfigManager::get_nested_value(const std::string& key) const {
    auto key_parts = split_key(key);
    const Json* current = &config_;

    for (const auto& part : key_parts) {
        if (!current->is_object() || !current->contains(part)) {
            return Json{}; // Return null JSON
        }
        current = &(*current)[part];
    }

    return *current;
}

void ConfigManager::set_nested_value(const std::string& key, const Json& value) {
    auto key_parts = split_key(key);
    Json* current = &config_;

    // Navigate to the parent of the target key
    for (size_t i = 0; i < key_parts.size() - 1; ++i) {
        const auto& part = key_parts[i];

        if (!current->is_object()) {
            *current = Json::object();
        }

        if (!current->contains(part)) {
            (*current)[part] = Json::object();
        }

        current = &(*current)[part];
    }

    // Set the final value
    if (!current->is_object()) {
        *current = Json::object();
    }

    (*current)[key_parts.back()] = value;
}

bool ConfigManager::remove_nested_value(const std::string& key) {
    auto key_parts = split_key(key);
    Json* current = &config_;

    // Navigate to the parent of the target key
    for (size_t i = 0; i < key_parts.size() - 1; ++i) {
        const auto& part = key_parts[i];

        if (!current->is_object() || !current->contains(part)) {
            return false; // Path doesn't exist
        }

        current = &(*current)[part];
    }

    // Remove the final key
    if (current->is_object() && current->contains(key_parts.back())) {
        current->erase(key_parts.back());
        return true;
    }

    return false;
}

void ConfigManager::notify_change(const std::string& key, const Json& old_value, const Json& new_value) {
    std::lock_guard lock(callbacks_mutex_);

    auto it = change_callbacks_.find(key);
    if (it != change_callbacks_.end()) {
        try {
            it->second(key, old_value, new_value);
        } catch (const std::exception& e) {
            logger_->error("Exception in config change callback for key '{}': {}", key, e.what());
        }
    }
}

void ConfigManager::file_watcher_loop() {
    while (file_watching_active_.load()) {
        try {
            if (std::filesystem::exists(watched_file_)) {
                auto current_write_time = std::filesystem::last_write_time(watched_file_);

                if (current_write_time != last_write_time_) {
                    logger_->info("Configuration file changed, reloading: {}", watched_file_);

                    if (load_from_file(watched_file_)) {
                        last_write_time_ = current_write_time;
                        logger_->info("Configuration reloaded successfully");
                    } else {
                        logger_->error("Failed to reload configuration file");
                    }
                }
            }

            // Check every second
            std::this_thread::sleep_for(std::chrono::seconds(1));

        } catch (const std::exception& e) {
            logger_->error("Exception in file watcher loop: {}", e.what());
            std::this_thread::sleep_for(std::chrono::seconds(5));
        }
    }
}

std::vector<std::string> ConfigManager::split_key(const std::string& key) const {
    std::vector<std::string> parts;
    std::stringstream ss(key);
    std::string part;

    while (std::getline(ss, part, '.')) {
        if (!part.empty()) {
            parts.push_back(part);
        }
    }

    return parts;
}

Json* ConfigManager::get_nested_object(const std::vector<std::string>& key_parts, bool create_if_missing) {
    Json* current = &config_;

    for (const auto& part : key_parts) {
        if (!current->is_object()) {
            if (create_if_missing) {
                *current = Json::object();
            } else {
                return nullptr;
            }
        }

        if (!current->contains(part)) {
            if (create_if_missing) {
                (*current)[part] = Json::object();
            } else {
                return nullptr;
            }
        }

        current = &(*current)[part];
    }

    return current;
}

const Json* ConfigManager::get_nested_object(const std::vector<std::string>& key_parts) const {
    const Json* current = &config_;

    for (const auto& part : key_parts) {
        if (!current->is_object() || !current->contains(part)) {
            return nullptr;
        }
        current = &(*current)[part];
    }

    return current;
}

// Schema namespace implementation

namespace Schema {

Json get_default_schema() {
    Json schema = {
        {"type", "object"},
        {"required", Json::array({"server", "services"})},
        {"properties", {
            {"server", get_server_schema()},
            {"services", {
                {"type", "object"},
                {"properties", {
                    {"datahub", get_datahub_schema()},
                    {"trading", get_trading_schema()}
                }}
            }},
            {"logging", {
                {"type", "object"},
                {"properties", {
                    {"level", {"type", "string"}},
                    {"file", {"type", "string"}},
                    {"console", {"type", "boolean"}}
                }}
            }}
        }}
    };
    return schema;
}

Json get_datahub_schema() {
    Json schema = {
        {"type", "object"},
        {"properties", {
            {"enabled", {"type", "boolean"}},
            {"config_file", {"type", "string"}},
            {"database", {
                {"type", "object"},
                {"properties", {
                    {"type", {"type", "string"}},
                    {"connection_string", {"type", "string"}}
                }}
            }}
        }}
    };
    return schema;
}

Json get_trading_schema() {
    Json schema = {
        {"type", "object"},
        {"properties", {
            {"enabled", {"type", "boolean"}},
            {"config_file", {"type", "string"}},
            {"server", {
                {"type", "object"},
                {"properties", {
                    {"port", {"type", "number"}},
                    {"network_threads", {"type", "number"}}
                }}
            }}
        }}
    };
    return schema;
}

Json get_server_schema() {
    Json schema = {
        {"type", "object"},
        {"required", Json::array({"host", "port"})},
        {"properties", {
            {"host", {"type", "string"}},
            {"port", {"type", "number", "minimum", 1, "maximum", 65535}},
            {"worker_threads", {"type", "number", "minimum", 1}},
            {"api_prefix", {"type", "string"}}
        }}
    };
    return schema;
}

} // namespace Schema

// ConfigUtils namespace implementation

namespace ConfigUtils {

Json load_config_with_priority(const std::vector<std::string>& file_paths,
                               const std::string& env_prefix) {
    Json config = Json::object();

    // Load from files in order (later files override earlier ones)
    for (const auto& file_path : file_paths) {
        if (std::filesystem::exists(file_path)) {
            try {
                std::ifstream file(file_path);
                Json file_config;
                file >> file_config;

                // Merge with existing config
                for (const auto& [key, value] : file_config.items()) {
                    config[key] = value;
                }

            } catch (const std::exception& e) {
                spdlog::warn("Failed to load config file {}: {}", file_path, e.what());
            }
        }
    }

    // Load environment variables (highest priority)
    ConfigManager temp_manager;
    temp_manager.load_from_json(config);
    temp_manager.load_from_environment(env_prefix);
    config = temp_manager.get_all();

    return config;
}

Json expand_environment_variables(const Json& config) {
    Json expanded = config;

    std::function<void(Json&)> expand_recursive = [&](Json& obj) {
        if (obj.is_string()) {
            std::string str = obj.get<std::string>();

            // Simple environment variable expansion: ${VAR_NAME}
            std::regex env_regex(R"(\$\{([^}]+)\})");
            std::smatch match;

            while (std::regex_search(str, match, env_regex)) {
                std::string var_name = match[1].str();
                const char* env_value = std::getenv(var_name.c_str());

                if (env_value) {
                    str.replace(match.position(), match.length(), env_value);
                } else {
                    // Keep the original if environment variable not found
                    break;
                }
            }

            obj = str;

        } else if (obj.is_object()) {
            for (auto& [key, value] : obj.items()) {
                expand_recursive(value);
            }
        } else if (obj.is_array()) {
            for (auto& item : obj) {
                expand_recursive(item);
            }
        }
    };

    expand_recursive(expanded);
    return expanded;
}

bool validate_required_keys(const Json& config, const std::vector<std::string>& required_keys) {
    for (const auto& key : required_keys) {
        if (!config.contains(key)) {
            return false;
        }
    }
    return true;
}

Json get_config_template() {
    Json template_config = {
        {"server", {
            {"host", "0.0.0.0"},
            {"port", 8080},
            {"worker_threads", 4},
            {"api_prefix", "/api/v1"}
        }},
        {"services", {
            {"datahub", {
                {"enabled", true},
                {"config_file", "config/datahub.json"}
            }},
            {"trading", {
                {"enabled", true},
                {"config_file", "config/trading.json"}
            }}
        }},
        {"logging", {
            {"level", "info"},
            {"file", "logs/quantservices.log"},
            {"console", true}
        }},
        {"health_monitoring", {
            {"enabled", true},
            {"check_interval_seconds", 30}
        }}
    };
    return template_config;
}

bool create_default_config_file(const std::string& file_path) {
    try {
        auto dir = std::filesystem::path(file_path).parent_path();
        if (!dir.empty() && !std::filesystem::exists(dir)) {
            std::filesystem::create_directories(dir);
        }

        std::ofstream file(file_path);
        if (!file.is_open()) {
            return false;
        }

        Json template_config = get_config_template();
        file << template_config.dump(2);

        return true;

    } catch (const std::exception&) {
        return false;
    }
}

} // namespace ConfigUtils

} // namespace QuantServices
