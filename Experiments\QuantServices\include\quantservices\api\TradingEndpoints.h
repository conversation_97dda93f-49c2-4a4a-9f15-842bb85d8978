/**
 * @file TradingEndpoints.h
 * @brief REST API endpoints for Trading functionality
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include "../RestApiServer.h"
#include <memory>

// Forward declarations
namespace RoboQuant::Trading {
    class TradingServer;
}

namespace QuantServices {

/**
 * @brief Trading API endpoints handler
 */
class TradingEndpoints {
public:
    /**
     * @brief Constructor
     */
    explicit TradingEndpoints(std::shared_ptr<RoboQuant::Trading::TradingServer> trading_server);
    
    /**
     * @brief Destructor
     */
    ~TradingEndpoints() = default;
    
    // Non-copyable, non-movable
    TradingEndpoints(const TradingEndpoints&) = delete;
    TradingEndpoints& operator=(const TradingEndpoints&) = delete;
    TradingEndpoints(TradingEndpoints&&) = delete;
    TradingEndpoints& operator=(TradingEndpoints&&) = delete;
    
    /**
     * @brief Register all Trading endpoints with the server
     */
    void register_endpoints(RestApiServer& server);

private:
    // Trading server
    std::shared_ptr<RoboQuant::Trading::TradingServer> trading_server_;
    
    // Strategy management endpoints
    ApiResponse get_strategies(const RequestContext& context);
    ApiResponse get_strategy(const RequestContext& context);
    ApiResponse create_strategy(const RequestContext& context);
    ApiResponse update_strategy(const RequestContext& context);
    ApiResponse delete_strategy(const RequestContext& context);
    ApiResponse start_strategy(const RequestContext& context);
    ApiResponse stop_strategy(const RequestContext& context);
    ApiResponse get_strategy_status(const RequestContext& context);
    ApiResponse get_strategy_performance(const RequestContext& context);
    
    // Portfolio management endpoints
    ApiResponse get_portfolios(const RequestContext& context);
    ApiResponse get_portfolio(const RequestContext& context);
    ApiResponse create_portfolio(const RequestContext& context);
    ApiResponse update_portfolio(const RequestContext& context);
    ApiResponse delete_portfolio(const RequestContext& context);
    ApiResponse get_portfolio_positions(const RequestContext& context);
    ApiResponse get_portfolio_performance(const RequestContext& context);
    ApiResponse get_portfolio_risk_metrics(const RequestContext& context);
    
    // Order management endpoints
    ApiResponse get_orders(const RequestContext& context);
    ApiResponse get_order(const RequestContext& context);
    ApiResponse create_order(const RequestContext& context);
    ApiResponse update_order(const RequestContext& context);
    ApiResponse cancel_order(const RequestContext& context);
    ApiResponse get_order_status(const RequestContext& context);
    ApiResponse get_order_fills(const RequestContext& context);
    
    // Position management endpoints
    ApiResponse get_positions(const RequestContext& context);
    ApiResponse get_position(const RequestContext& context);
    ApiResponse close_position(const RequestContext& context);
    ApiResponse get_position_history(const RequestContext& context);
    
    // Risk management endpoints
    ApiResponse get_risk_limits(const RequestContext& context);
    ApiResponse update_risk_limits(const RequestContext& context);
    ApiResponse get_risk_metrics(const RequestContext& context);
    ApiResponse get_risk_alerts(const RequestContext& context);
    
    // Model management endpoints
    ApiResponse get_models(const RequestContext& context);
    ApiResponse get_model(const RequestContext& context);
    ApiResponse create_model(const RequestContext& context);
    ApiResponse update_model(const RequestContext& context);
    ApiResponse delete_model(const RequestContext& context);
    ApiResponse run_model_prediction(const RequestContext& context);
    ApiResponse get_model_performance(const RequestContext& context);
    
    // Trading server status endpoints
    ApiResponse get_trading_status(const RequestContext& context);
    ApiResponse get_trading_health(const RequestContext& context);
    ApiResponse get_trading_metrics(const RequestContext& context);
    ApiResponse start_trading_server(const RequestContext& context);
    ApiResponse stop_trading_server(const RequestContext& context);
    ApiResponse restart_trading_server(const RequestContext& context);
    
    // Market data integration endpoints
    ApiResponse get_market_data_status(const RequestContext& context);
    ApiResponse subscribe_market_data(const RequestContext& context);
    ApiResponse unsubscribe_market_data(const RequestContext& context);
    
    // Backtesting endpoints
    ApiResponse create_backtest(const RequestContext& context);
    ApiResponse get_backtest_results(const RequestContext& context);
    ApiResponse get_backtest_status(const RequestContext& context);
    ApiResponse delete_backtest(const RequestContext& context);
    
    // Utility methods
    bool validate_strategy_id(const std::string& strategy_id) const;
    bool validate_portfolio_id(const std::string& portfolio_id) const;
    bool validate_order_id(const std::string& order_id) const;
    bool validate_symbol(const std::string& symbol) const;
    bool validate_order_request(const Json& order_data) const;
    bool validate_strategy_config(const Json& strategy_config) const;
    
    Json strategy_to_json(const Json& strategy_data) const;
    Json portfolio_to_json(const Json& portfolio_data) const;
    Json order_to_json(const Json& order_data) const;
    Json position_to_json(const Json& position_data) const;
    Json model_to_json(const Json& model_data) const;
    
    std::string extract_path_param(const RequestContext& context, const std::string& param_name) const;
    Json extract_query_params(const RequestContext& context) const;
    
    // Error handling
    ApiResponse create_validation_error(const std::string& field, const std::string& message) const;
    ApiResponse create_not_found_error(const std::string& resource, const std::string& id) const;
    ApiResponse create_trading_error(const std::string& message) const;
    ApiResponse create_insufficient_funds_error() const;
    ApiResponse create_risk_limit_error(const std::string& limit_type) const;
};

/**
 * @brief Trading endpoint registration helper
 */
namespace TradingEndpointHelper {

/**
 * @brief Register all Trading endpoints
 */
void register_all_endpoints(RestApiServer& server, 
                           std::shared_ptr<RoboQuant::Trading::TradingServer> trading_server);

/**
 * @brief Get Trading API documentation
 */
Json get_api_documentation();

/**
 * @brief Get Trading endpoint schemas
 */
Json get_endpoint_schemas();

/**
 * @brief Get order request schema
 */
Json get_order_request_schema();

/**
 * @brief Get strategy configuration schema
 */
Json get_strategy_config_schema();

/**
 * @brief Get portfolio configuration schema
 */
Json get_portfolio_config_schema();

} // namespace TradingEndpointHelper

} // namespace QuantServices
