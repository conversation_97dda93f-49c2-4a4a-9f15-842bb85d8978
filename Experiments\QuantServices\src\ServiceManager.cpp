/**
 * @file ServiceManager.cpp
 * @brief Implementation of ServiceManager class
 * <AUTHOR> Team
 * @date 2024
 */

#include "quantservices/ServiceManager.h"
#include <spdlog/spdlog.h>
#include <chrono>
#include <thread>

// Include DataHub headers if available
#ifdef DATAHUB_AVAILABLE
#include "services/DataHubManager.h"
#include "api/DataHubAPI.h"
#endif

// Include Trading headers if available
#ifdef TRADING_AVAILABLE
#include "trading/TradingServer.h"
#include "trading/Trading.h"
#endif

namespace QuantServices {

// ServiceInfo implementation
Json ServiceInfo::to_json() const {
    Json j;
    j["name"] = name;
    j["version"] = version;
    j["status"] = static_cast<int>(status);
    j["start_time"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        start_time.time_since_epoch()).count();
    j["last_heartbeat"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        last_heartbeat.time_since_epoch()).count();
    j["metadata"] = metadata;
    return j;
}

// ServiceManager implementation
ServiceManager::ServiceManager() 
    : logger_(spdlog::get("quantservices") ? spdlog::get("quantservices") : spdlog::default_logger()) {
    
    // Initialize service info
    services_info_[DATAHUB_SERVICE] = ServiceInfo{
        DATAHUB_SERVICE, "1.0.0", ServiceStatus::Stopped,
        std::chrono::system_clock::now(), std::chrono::system_clock::now(),
        Json::object()
    };
    
    services_info_[TRADING_SERVICE] = ServiceInfo{
        TRADING_SERVICE, "1.0.0", ServiceStatus::Stopped,
        std::chrono::system_clock::now(), std::chrono::system_clock::now(),
        Json::object()
    };
    
    // Enable services by default
    services_enabled_[DATAHUB_SERVICE] = true;
    services_enabled_[TRADING_SERVICE] = true;
    
    logger_->info("ServiceManager initialized");
}

ServiceManager::~ServiceManager() {
    if (health_monitoring_active_.load()) {
        stop_health_monitoring();
    }
    
    // Stop all services
    auto stop_future = stop_all();
    stop_future.wait();
    
    logger_->info("ServiceManager destroyed");
}

std::future<bool> ServiceManager::initialize(const Json& config) {
    return std::async(std::launch::async, [this, config]() -> bool {
        try {
            config_ = config;
            
            logger_->info("Initializing services...");
            
            bool success = true;
            
            // Initialize DataHub if enabled
            if (is_service_enabled(DATAHUB_SERVICE)) {
                Json datahub_config = config.value("datahub", Json::object());
                if (!initialize_datahub(datahub_config)) {
                    logger_->error("Failed to initialize DataHub service");
                    success = false;
                } else {
                    logger_->info("DataHub service initialized successfully");
                }
            } else {
                logger_->info("DataHub service disabled");
            }
            
            // Initialize Trading if enabled
            if (is_service_enabled(TRADING_SERVICE)) {
                Json trading_config = config.value("trading", Json::object());
                if (!initialize_trading(trading_config)) {
                    logger_->error("Failed to initialize Trading service");
                    success = false;
                } else {
                    logger_->info("Trading service initialized successfully");
                }
            } else {
                logger_->info("Trading service disabled");
            }
            
            if (success) {
                start_health_monitoring();
                logger_->info("All services initialized successfully");
            }
            
            return success;
            
        } catch (const std::exception& e) {
            logger_->error("Exception during service initialization: {}", e.what());
            return false;
        }
    });
}

std::future<bool> ServiceManager::start_all() {
    return std::async(std::launch::async, [this]() -> bool {
        try {
            logger_->info("Starting all services...");
            
            bool success = true;
            
            // Start DataHub
            if (is_service_enabled(DATAHUB_SERVICE)) {
                auto datahub_future = start_datahub();
                if (!datahub_future.get()) {
                    logger_->error("Failed to start DataHub service");
                    success = false;
                }
            }
            
            // Start Trading
            if (is_service_enabled(TRADING_SERVICE)) {
                auto trading_future = start_trading();
                if (!trading_future.get()) {
                    logger_->error("Failed to start Trading service");
                    success = false;
                }
            }
            
            if (success) {
                logger_->info("All services started successfully");
            }
            
            return success;
            
        } catch (const std::exception& e) {
            logger_->error("Exception during service startup: {}", e.what());
            return false;
        }
    });
}

std::future<void> ServiceManager::stop_all() {
    return std::async(std::launch::async, [this]() {
        try {
            logger_->info("Stopping all services...");
            
            stop_health_monitoring();
            
            // Stop Trading first
            if (trading_server_) {
                auto trading_future = stop_trading();
                trading_future.wait();
            }
            
            // Stop DataHub
            if (datahub_manager_) {
                auto datahub_future = stop_datahub();
                datahub_future.wait();
            }
            
            logger_->info("All services stopped");
            
        } catch (const std::exception& e) {
            logger_->error("Exception during service shutdown: {}", e.what());
        }
    });
}

std::future<bool> ServiceManager::start_service(const std::string& service_name) {
    return std::async(std::launch::async, [this, service_name]() -> bool {
        try {
            if (service_name == DATAHUB_SERVICE) {
                return start_datahub().get();
            } else if (service_name == TRADING_SERVICE) {
                return start_trading().get();
            } else {
                logger_->warn("Unknown service: {}", service_name);
                return false;
            }
        } catch (const std::exception& e) {
            logger_->error("Exception starting service {}: {}", service_name, e.what());
            return false;
        }
    });
}

std::future<bool> ServiceManager::stop_service(const std::string& service_name) {
    return std::async(std::launch::async, [this, service_name]() -> bool {
        try {
            if (service_name == DATAHUB_SERVICE) {
                return stop_datahub().get();
            } else if (service_name == TRADING_SERVICE) {
                return stop_trading().get();
            } else {
                logger_->warn("Unknown service: {}", service_name);
                return false;
            }
        } catch (const std::exception& e) {
            logger_->error("Exception stopping service {}: {}", service_name, e.what());
            return false;
        }
    });
}

std::future<bool> ServiceManager::restart_service(const std::string& service_name) {
    return std::async(std::launch::async, [this, service_name]() -> bool {
        try {
            logger_->info("Restarting service: {}", service_name);
            
            // Stop the service
            if (!stop_service(service_name).get()) {
                logger_->error("Failed to stop service: {}", service_name);
                return false;
            }
            
            // Wait a bit
            std::this_thread::sleep_for(std::chrono::seconds(1));
            
            // Start the service
            if (!start_service(service_name).get()) {
                logger_->error("Failed to start service: {}", service_name);
                return false;
            }
            
            logger_->info("Service restarted successfully: {}", service_name);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("Exception restarting service {}: {}", service_name, e.what());
            return false;
        }
    });
}

ServiceStatus ServiceManager::get_service_status(const std::string& service_name) const {
    std::shared_lock lock(services_mutex_);
    auto it = services_info_.find(service_name);
    if (it != services_info_.end()) {
        return it->second.status;
    }
    return ServiceStatus::Error;
}

std::unordered_map<std::string, ServiceInfo> ServiceManager::get_all_services_status() const {
    std::shared_lock lock(services_mutex_);
    return services_info_;
}

bool ServiceManager::are_all_services_running() const {
    std::shared_lock lock(services_mutex_);
    for (const auto& [name, info] : services_info_) {
        if (is_service_enabled(name) && info.status != ServiceStatus::Running) {
            return false;
        }
    }
    return true;
}

#ifdef DATAHUB_AVAILABLE
std::shared_ptr<DataHub::Services::IDataHubManager> ServiceManager::get_datahub_manager() const {
    return datahub_manager_;
}

std::shared_ptr<DataHub::API::DataHubAPI> ServiceManager::get_datahub_api() const {
    return datahub_api_;
}
#else
std::shared_ptr<void> ServiceManager::get_datahub_manager() const {
    return nullptr;
}

std::shared_ptr<void> ServiceManager::get_datahub_api() const {
    return nullptr;
}
#endif

#ifdef TRADING_AVAILABLE
std::shared_ptr<RoboQuant::Trading::TradingServer> ServiceManager::get_trading_server() const {
    return trading_server_;
}
#else
std::shared_ptr<void> ServiceManager::get_trading_server() const {
    return nullptr;
}
#endif

void ServiceManager::set_service_event_callback(ServiceEventCallback callback) {
    std::lock_guard lock(event_callback_mutex_);
    event_callback_ = std::move(callback);
}

Json ServiceManager::get_health_metrics() const {
    Json metrics;
    
    std::shared_lock lock(services_mutex_);
    for (const auto& [name, info] : services_info_) {
        Json service_metrics;
        service_metrics["status"] = static_cast<int>(info.status);
        service_metrics["uptime_seconds"] = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now() - info.start_time).count();
        service_metrics["last_heartbeat"] = std::chrono::duration_cast<std::chrono::milliseconds>(
            info.last_heartbeat.time_since_epoch()).count();
        
        metrics[name] = service_metrics;
    }
    
    return metrics;
}

Json ServiceManager::get_performance_metrics() const {
    Json metrics;
    
    // Add basic performance metrics
    metrics["total_services"] = services_info_.size();
    metrics["running_services"] = 0;
    metrics["enabled_services"] = 0;
    
    std::shared_lock lock(services_mutex_);
    for (const auto& [name, info] : services_info_) {
        if (info.status == ServiceStatus::Running) {
            metrics["running_services"] = metrics["running_services"].get<int>() + 1;
        }
        if (is_service_enabled(name)) {
            metrics["enabled_services"] = metrics["enabled_services"].get<int>() + 1;
        }
    }
    
    return metrics;
}

void ServiceManager::enable_service(const std::string& service_name, bool enabled) {
    std::unique_lock lock(services_mutex_);
    services_enabled_[service_name] = enabled;
    logger_->info("Service {} {}", service_name, enabled ? "enabled" : "disabled");
}

bool ServiceManager::is_service_enabled(const std::string& service_name) const {
    std::shared_lock lock(services_mutex_);
    auto it = services_enabled_.find(service_name);
    return it != services_enabled_.end() ? it->second : false;
}

// Private methods implementation

bool ServiceManager::initialize_datahub(const Json& datahub_config) {
    try {
#ifdef DATAHUB_AVAILABLE
        // Create DataHub manager with configuration
        // This is a placeholder - actual implementation depends on DataHub_Modern API
        logger_->info("Initializing DataHub with config");

        // For now, create a mock implementation
        // In real implementation, this would create the actual DataHub manager
        update_service_info(DATAHUB_SERVICE, ServiceStatus::Stopped);

        return true;
#else
        logger_->warn("DataHub not available - compiled without DataHub support");
        update_service_info(DATAHUB_SERVICE, ServiceStatus::Error);
        return false;
#endif
    } catch (const std::exception& e) {
        logger_->error("Failed to initialize DataHub: {}", e.what());
        update_service_info(DATAHUB_SERVICE, ServiceStatus::Error);
        return false;
    }
}

bool ServiceManager::initialize_trading(const Json& trading_config) {
    try {
#ifdef TRADING_AVAILABLE
        // Create Trading server with configuration
        // This is a placeholder - actual implementation depends on TradingSvr_Modern API
        logger_->info("Initializing Trading server with config");

        // For now, create a mock implementation
        // In real implementation, this would create the actual Trading server
        update_service_info(TRADING_SERVICE, ServiceStatus::Stopped);

        return true;
#else
        logger_->warn("Trading server not available - compiled without Trading support");
        update_service_info(TRADING_SERVICE, ServiceStatus::Error);
        return false;
#endif
    } catch (const std::exception& e) {
        logger_->error("Failed to initialize Trading server: {}", e.what());
        update_service_info(TRADING_SERVICE, ServiceStatus::Error);
        return false;
    }
}

std::future<bool> ServiceManager::start_datahub() {
    return std::async(std::launch::async, [this]() -> bool {
        try {
            update_service_info(DATAHUB_SERVICE, ServiceStatus::Starting);

#ifdef DATAHUB_AVAILABLE
            if (datahub_manager_) {
                // Start DataHub manager
                // auto result = datahub_manager_->start();
                // if (result.is_success()) {
                    update_service_info(DATAHUB_SERVICE, ServiceStatus::Running);
                    emit_service_event(DATAHUB_SERVICE, "started", Json::object());
                    return true;
                // } else {
                //     logger_->error("Failed to start DataHub: {}", result.error_message());
                //     update_service_info(DATAHUB_SERVICE, ServiceStatus::Error);
                //     return false;
                // }
            }
#endif

            // Mock implementation for now
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            update_service_info(DATAHUB_SERVICE, ServiceStatus::Running);
            emit_service_event(DATAHUB_SERVICE, "started", Json::object());
            return true;

        } catch (const std::exception& e) {
            logger_->error("Exception starting DataHub: {}", e.what());
            update_service_info(DATAHUB_SERVICE, ServiceStatus::Error);
            return false;
        }
    });
}

std::future<bool> ServiceManager::start_trading() {
    return std::async(std::launch::async, [this]() -> bool {
        try {
            update_service_info(TRADING_SERVICE, ServiceStatus::Starting);

#ifdef TRADING_AVAILABLE
            if (trading_server_) {
                // Start Trading server
                // auto result = trading_server_->start();
                // if (result) {
                    update_service_info(TRADING_SERVICE, ServiceStatus::Running);
                    emit_service_event(TRADING_SERVICE, "started", Json::object());
                    return true;
                // } else {
                //     logger_->error("Failed to start Trading server");
                //     update_service_info(TRADING_SERVICE, ServiceStatus::Error);
                //     return false;
                // }
            }
#endif

            // Mock implementation for now
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            update_service_info(TRADING_SERVICE, ServiceStatus::Running);
            emit_service_event(TRADING_SERVICE, "started", Json::object());
            return true;

        } catch (const std::exception& e) {
            logger_->error("Exception starting Trading server: {}", e.what());
            update_service_info(TRADING_SERVICE, ServiceStatus::Error);
            return false;
        }
    });
}

std::future<bool> ServiceManager::stop_datahub() {
    return std::async(std::launch::async, [this]() -> bool {
        try {
            update_service_info(DATAHUB_SERVICE, ServiceStatus::Stopping);

#ifdef DATAHUB_AVAILABLE
            if (datahub_manager_) {
                // Stop DataHub manager
                // auto result = datahub_manager_->stop();
                // if (result.is_success()) {
                    update_service_info(DATAHUB_SERVICE, ServiceStatus::Stopped);
                    emit_service_event(DATAHUB_SERVICE, "stopped", Json::object());
                    return true;
                // }
            }
#endif

            // Mock implementation for now
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
            update_service_info(DATAHUB_SERVICE, ServiceStatus::Stopped);
            emit_service_event(DATAHUB_SERVICE, "stopped", Json::object());
            return true;

        } catch (const std::exception& e) {
            logger_->error("Exception stopping DataHub: {}", e.what());
            update_service_info(DATAHUB_SERVICE, ServiceStatus::Error);
            return false;
        }
    });
}

std::future<bool> ServiceManager::stop_trading() {
    return std::async(std::launch::async, [this]() -> bool {
        try {
            update_service_info(TRADING_SERVICE, ServiceStatus::Stopping);

#ifdef TRADING_AVAILABLE
            if (trading_server_) {
                // Stop Trading server
                // trading_server_->stop();
                update_service_info(TRADING_SERVICE, ServiceStatus::Stopped);
                emit_service_event(TRADING_SERVICE, "stopped", Json::object());
                return true;
            }
#endif

            // Mock implementation for now
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
            update_service_info(TRADING_SERVICE, ServiceStatus::Stopped);
            emit_service_event(TRADING_SERVICE, "stopped", Json::object());
            return true;

        } catch (const std::exception& e) {
            logger_->error("Exception stopping Trading server: {}", e.what());
            update_service_info(TRADING_SERVICE, ServiceStatus::Error);
            return false;
        }
    });
}

void ServiceManager::update_service_info(const std::string& service_name, ServiceStatus status) {
    std::unique_lock lock(services_mutex_);
    auto it = services_info_.find(service_name);
    if (it != services_info_.end()) {
        it->second.status = status;
        it->second.last_heartbeat = std::chrono::system_clock::now();

        if (status == ServiceStatus::Running && it->second.start_time == std::chrono::system_clock::time_point{}) {
            it->second.start_time = std::chrono::system_clock::now();
        }
    }
}

void ServiceManager::emit_service_event(const std::string& service_name,
                                       const std::string& event_type,
                                       const Json& event_data) {
    std::lock_guard lock(event_callback_mutex_);
    if (event_callback_) {
        try {
            event_callback_(service_name, event_type, event_data);
        } catch (const std::exception& e) {
            logger_->error("Exception in service event callback: {}", e.what());
        }
    }
}

void ServiceManager::start_health_monitoring() {
    if (health_monitoring_active_.load()) {
        return;
    }

    health_monitoring_active_.store(true);
    health_monitor_thread_ = std::make_unique<std::thread>(&ServiceManager::health_monitor_loop, this);
    logger_->info("Health monitoring started");
}

void ServiceManager::stop_health_monitoring() {
    if (!health_monitoring_active_.load()) {
        return;
    }

    health_monitoring_active_.store(false);
    if (health_monitor_thread_ && health_monitor_thread_->joinable()) {
        health_monitor_thread_->join();
    }
    health_monitor_thread_.reset();
    logger_->info("Health monitoring stopped");
}

void ServiceManager::health_monitor_loop() {
    while (health_monitoring_active_.load()) {
        try {
            check_datahub_health();
            check_trading_health();

            // Sleep for health check interval
            std::this_thread::sleep_for(std::chrono::seconds(30));

        } catch (const std::exception& e) {
            logger_->error("Exception in health monitor loop: {}", e.what());
            std::this_thread::sleep_for(std::chrono::seconds(5));
        }
    }
}

void ServiceManager::check_datahub_health() {
    if (!is_service_enabled(DATAHUB_SERVICE)) {
        return;
    }

    try {
#ifdef DATAHUB_AVAILABLE
        if (datahub_manager_) {
            // Check DataHub health
            // auto health_result = datahub_manager_->health_check();
            // if (health_result.is_success()) {
                update_service_info(DATAHUB_SERVICE, ServiceStatus::Running);
            // } else {
            //     logger_->warn("DataHub health check failed: {}", health_result.error_message());
            //     update_service_info(DATAHUB_SERVICE, ServiceStatus::Error);
            // }
        }
#endif

        // Mock health check for now
        auto current_status = get_service_status(DATAHUB_SERVICE);
        if (current_status == ServiceStatus::Running) {
            update_service_info(DATAHUB_SERVICE, ServiceStatus::Running);
        }

    } catch (const std::exception& e) {
        logger_->error("Exception checking DataHub health: {}", e.what());
        update_service_info(DATAHUB_SERVICE, ServiceStatus::Error);
    }
}

void ServiceManager::check_trading_health() {
    if (!is_service_enabled(TRADING_SERVICE)) {
        return;
    }

    try {
#ifdef TRADING_AVAILABLE
        if (trading_server_) {
            // Check Trading server health
            // auto status = trading_server_->get_status();
            // if (status == TradingServerStatus::Running) {
                update_service_info(TRADING_SERVICE, ServiceStatus::Running);
            // } else {
            //     logger_->warn("Trading server not running");
            //     update_service_info(TRADING_SERVICE, ServiceStatus::Error);
            // }
        }
#endif

        // Mock health check for now
        auto current_status = get_service_status(TRADING_SERVICE);
        if (current_status == ServiceStatus::Running) {
            update_service_info(TRADING_SERVICE, ServiceStatus::Running);
        }

    } catch (const std::exception& e) {
        logger_->error("Exception checking Trading server health: {}", e.what());
        update_service_info(TRADING_SERVICE, ServiceStatus::Error);
    }
}

// ServiceManagerBuilder implementation
ServiceManagerBuilder& ServiceManagerBuilder::with_datahub_config(const Json& config) {
    datahub_config_ = config;
    return *this;
}

ServiceManagerBuilder& ServiceManagerBuilder::with_trading_config(const Json& config) {
    trading_config_ = config;
    return *this;
}

ServiceManagerBuilder& ServiceManagerBuilder::with_health_monitoring(bool enabled) {
    health_monitoring_enabled_ = enabled;
    return *this;
}

ServiceManagerBuilder& ServiceManagerBuilder::with_event_callback(ServiceEventCallback callback) {
    event_callback_ = std::move(callback);
    return *this;
}

std::unique_ptr<ServiceManager> ServiceManagerBuilder::build() {
    auto manager = std::make_unique<ServiceManager>();

    if (event_callback_) {
        manager->set_service_event_callback(event_callback_);
    }

    Json config;
    if (!datahub_config_.is_null()) {
        config["datahub"] = datahub_config_;
    }
    if (!trading_config_.is_null()) {
        config["trading"] = trading_config_;
    }

    // Initialize with config
    auto init_future = manager->initialize(config);
    init_future.wait(); // Wait for initialization to complete

    return manager;
}

} // namespace QuantServices
