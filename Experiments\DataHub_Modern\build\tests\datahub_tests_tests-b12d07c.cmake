add_test( [==[Types - Enum conversions]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[Types - Enum conversions]==]  )
set_tests_properties( [==[Types - Enum conversions]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[Types - Result class]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[Types - Result class]==]  )
set_tests_properties( [==[Types - Result class]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[Types - Utility functions]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[Types - Utility functions]==]  )
set_tests_properties( [==[Types - Utility functions]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[MarketData - QuoteData]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[MarketData - QuoteData]==]  )
set_tests_properties( [==[MarketData - QuoteData]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[MarketData - BarData]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[MarketData - BarData]==]  )
set_tests_properties( [==[MarketData - BarData]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[MarketData - TickData]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[MarketData - TickData]==]  )
set_tests_properties( [==[MarketData - TickData]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[MarketData - Utility functions]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[MarketData - Utility functions]==]  )
set_tests_properties( [==[MarketData - Utility functions]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[MarketData - Advanced tests]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[MarketData - Advanced tests]==]  )
set_tests_properties( [==[MarketData - Advanced tests]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[SecurityInfo - Basic functionality]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[SecurityInfo - Basic functionality]==]  )
set_tests_properties( [==[SecurityInfo - Basic functionality]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[BlockInfo - Block management]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[BlockInfo - Block management]==]  )
set_tests_properties( [==[BlockInfo - Block management]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[Data Aggregation - Bar aggregation]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[Data Aggregation - Bar aggregation]==]  )
set_tests_properties( [==[Data Aggregation - Bar aggregation]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[Data Aggregation - Quote aggregation]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[Data Aggregation - Quote aggregation]==]  )
set_tests_properties( [==[Data Aggregation - Quote aggregation]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[Data Aggregation - Statistical calculations]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[Data Aggregation - Statistical calculations]==]  )
set_tests_properties( [==[Data Aggregation - Statistical calculations]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[Data Aggregation - Time-based grouping]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[Data Aggregation - Time-based grouping]==]  )
set_tests_properties( [==[Data Aggregation - Time-based grouping]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[Performance - Large data operations]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[Performance - Large data operations]==]  )
set_tests_properties( [==[Performance - Large data operations]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[Performance - Memory usage]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[Performance - Memory usage]==]  )
set_tests_properties( [==[Performance - Memory usage]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[Performance - Concurrent operations]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[Performance - Concurrent operations]==]  )
set_tests_properties( [==[Performance - Concurrent operations]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
set( datahub_tests_TESTS [==[Types - Enum conversions]==] [==[Types - Result class]==] [==[Types - Utility functions]==] [==[MarketData - QuoteData]==] [==[MarketData - BarData]==] [==[MarketData - TickData]==] [==[MarketData - Utility functions]==] [==[MarketData - Advanced tests]==] [==[SecurityInfo - Basic functionality]==] [==[BlockInfo - Block management]==] [==[Data Aggregation - Bar aggregation]==] [==[Data Aggregation - Quote aggregation]==] [==[Data Aggregation - Statistical calculations]==] [==[Data Aggregation - Time-based grouping]==] [==[Performance - Large data operations]==] [==[Performance - Memory usage]==] [==[Performance - Concurrent operations]==])
