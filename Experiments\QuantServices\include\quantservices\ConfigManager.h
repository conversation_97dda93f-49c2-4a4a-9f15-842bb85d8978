/**
 * @file ConfigManager.h
 * @brief Configuration management for QuantServices
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include <string>
#include <unordered_map>
#include <mutex>
#include <functional>
#include <filesystem>

#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>

namespace QuantServices {

using Json = nlohmann::json;

/**
 * @brief Configuration change callback type
 */
using ConfigChangeCallback = std::function<void(const std::string& key, const Json& old_value, const Json& new_value)>;

/**
 * @brief Configuration manager class
 */
class ConfigManager {
public:
    /**
     * @brief Constructor
     */
    explicit ConfigManager();
    
    /**
     * @brief Destructor
     */
    ~ConfigManager();
    
    // Non-copyable, non-movable
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;
    ConfigManager(ConfigManager&&) = delete;
    ConfigManager& operator=(ConfigManager&&) = delete;
    
    /**
     * @brief Load configuration from file
     */
    bool load_from_file(const std::string& file_path);
    
    /**
     * @brief Load configuration from JSON
     */
    bool load_from_json(const Json& config);
    
    /**
     * @brief Load configuration from environment variables
     */
    void load_from_environment(const std::string& prefix = "QUANTSERVICES_");
    
    /**
     * @brief Save configuration to file
     */
    bool save_to_file(const std::string& file_path) const;
    
    /**
     * @brief Get configuration value
     */
    template<typename T>
    T get(const std::string& key, const T& default_value = T{}) const;
    
    /**
     * @brief Set configuration value
     */
    template<typename T>
    void set(const std::string& key, const T& value);
    
    /**
     * @brief Check if key exists
     */
    bool has(const std::string& key) const;
    
    /**
     * @brief Remove configuration key
     */
    bool remove(const std::string& key);
    
    /**
     * @brief Get all configuration as JSON
     */
    Json get_all() const;
    
    /**
     * @brief Get configuration section
     */
    Json get_section(const std::string& section) const;
    
    /**
     * @brief Set configuration section
     */
    void set_section(const std::string& section, const Json& config);
    
    /**
     * @brief Register configuration change callback
     */
    void register_change_callback(const std::string& key, ConfigChangeCallback callback);
    
    /**
     * @brief Unregister configuration change callback
     */
    void unregister_change_callback(const std::string& key);
    
    /**
     * @brief Enable file watching for automatic reload
     */
    void enable_file_watching(const std::string& file_path);
    
    /**
     * @brief Disable file watching
     */
    void disable_file_watching();
    
    /**
     * @brief Validate configuration against schema
     */
    bool validate_schema(const Json& schema) const;
    
    /**
     * @brief Get configuration validation errors
     */
    std::vector<std::string> get_validation_errors(const Json& schema) const;
    
    /**
     * @brief Merge configuration with another
     */
    void merge(const Json& other_config);
    
    /**
     * @brief Get configuration as string (formatted JSON)
     */
    std::string to_string(int indent = 2) const;

private:
    // Configuration data
    mutable std::shared_mutex config_mutex_;
    Json config_;
    
    // File watching
    std::string watched_file_;
    std::unique_ptr<std::thread> file_watcher_thread_;
    std::atomic<bool> file_watching_active_{false};
    std::filesystem::file_time_type last_write_time_;
    
    // Change callbacks
    std::mutex callbacks_mutex_;
    std::unordered_map<std::string, ConfigChangeCallback> change_callbacks_;
    
    // Logging
    std::shared_ptr<spdlog::logger> logger_;
    
    // Internal methods
    Json get_nested_value(const std::string& key) const;
    void set_nested_value(const std::string& key, const Json& value);
    bool remove_nested_value(const std::string& key);
    
    void notify_change(const std::string& key, const Json& old_value, const Json& new_value);
    void file_watcher_loop();
    
    std::vector<std::string> split_key(const std::string& key) const;
    Json* get_nested_object(const std::vector<std::string>& key_parts, bool create_if_missing = false);
    const Json* get_nested_object(const std::vector<std::string>& key_parts) const;
};

/**
 * @brief Template implementations
 */
template<typename T>
T ConfigManager::get(const std::string& key, const T& default_value) const {
    std::shared_lock lock(config_mutex_);
    
    try {
        Json value = get_nested_value(key);
        if (value.is_null()) {
            return default_value;
        }
        return value.get<T>();
    } catch (const std::exception& e) {
        if (logger_) {
            logger_->warn("Failed to get config value for key '{}': {}", key, e.what());
        }
        return default_value;
    }
}

template<typename T>
void ConfigManager::set(const std::string& key, const T& value) {
    Json old_value;
    
    {
        std::unique_lock lock(config_mutex_);
        old_value = get_nested_value(key);
        set_nested_value(key, Json(value));
    }
    
    notify_change(key, old_value, Json(value));
}

/**
 * @brief Configuration schema definitions
 */
namespace Schema {

/**
 * @brief Get default QuantServices configuration schema
 */
Json get_default_schema();

/**
 * @brief Get DataHub configuration schema
 */
Json get_datahub_schema();

/**
 * @brief Get Trading configuration schema
 */
Json get_trading_schema();

/**
 * @brief Get Server configuration schema
 */
Json get_server_schema();

} // namespace Schema

/**
 * @brief Configuration utilities
 */
namespace ConfigUtils {

/**
 * @brief Load configuration from multiple sources with priority
 */
Json load_config_with_priority(const std::vector<std::string>& file_paths, 
                               const std::string& env_prefix = "QUANTSERVICES_");

/**
 * @brief Expand environment variables in configuration values
 */
Json expand_environment_variables(const Json& config);

/**
 * @brief Validate required configuration keys
 */
bool validate_required_keys(const Json& config, const std::vector<std::string>& required_keys);

/**
 * @brief Get configuration template
 */
Json get_config_template();

/**
 * @brief Create default configuration file
 */
bool create_default_config_file(const std::string& file_path);

} // namespace ConfigUtils

} // namespace QuantServices
