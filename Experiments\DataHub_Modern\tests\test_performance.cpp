#include <catch2/catch_test_macros.hpp>
#include "core/MarketData.h"
#include "core/Types.h"
#include <chrono>
#include <vector>
#include <random>
#include <thread>
#include <algorithm>
#include <numeric>
#include <limits>

using namespace DataHub::Core;

TEST_CASE("Performance - Large data operations", "[performance]") {
    SECTION("Create and process large number of bars") {
        const size_t num_bars = 10000;
        std::vector<BarData> bars;
        bars.reserve(num_bars);
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // Create bars
        for (size_t i = 0; i < num_bars; ++i) {
            BarData bar("000001.SZ", now(), BarSize::Minute1, BarType::Candle);
            bar.open = 10.0 + (i % 100) * 0.01;
            bar.high = bar.open + 0.05;
            bar.low = bar.open - 0.03;
            bar.close = bar.open + (i % 2 == 0 ? 0.02 : -0.01);
            bar.volume = 1000 + (i % 500);
            bar.amount = bar.close * bar.volume;
            bars.push_back(bar);
        }
        
        auto creation_time = std::chrono::high_resolution_clock::now();
        auto creation_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            creation_time - start_time).count();
        
        // Process bars (calculate statistics)
        double total_volume = 0.0;
        double total_amount = 0.0;
        double max_price = 0.0;
        double min_price = std::numeric_limits<double>::max();
        
        for (const auto& bar : bars) {
            total_volume += bar.volume;
            total_amount += bar.amount;
            max_price = std::max(max_price, bar.high);
            min_price = std::min(min_price, bar.low);
        }
        
        auto processing_time = std::chrono::high_resolution_clock::now();
        auto processing_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            processing_time - creation_time).count();
        
        // Verify results
        REQUIRE(bars.size() == num_bars);
        REQUIRE(total_volume > 0.0);
        REQUIRE(total_amount > 0.0);
        REQUIRE(max_price > min_price);
        
        // Performance assertions (these are rough guidelines)
        REQUIRE(creation_duration < 1000);    // Should create 10k bars in less than 1 second
        REQUIRE(processing_duration < 100);   // Should process 10k bars in less than 100ms
        
        INFO("Created " << num_bars << " bars in " << creation_duration << "ms");
        INFO("Processed " << num_bars << " bars in " << processing_duration << "ms");
    }
    
    SECTION("Quote data processing performance") {
        const size_t num_quotes = 50000;
        std::vector<QuoteData> quotes;
        quotes.reserve(num_quotes);
        
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<> price_dist(9.5, 10.5);
        std::uniform_int_distribution<> volume_dist(100, 5000);
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // Create quotes with random data
        for (size_t i = 0; i < num_quotes; ++i) {
            QuoteData quote;
            quote.symbol = "000001.SZ";
            quote.timestamp = now();
            quote.last_price = price_dist(gen);
            quote.bid_prices[0] = quote.last_price - 0.01;
            quote.ask_prices[0] = quote.last_price + 0.01;
            quote.bid_volumes[0] = volume_dist(gen);
            quote.ask_volumes[0] = volume_dist(gen);
            quote.volume = quote.bid_volumes[0] + quote.ask_volumes[0];
            quotes.push_back(quote);
        }
        
        auto creation_time = std::chrono::high_resolution_clock::now();
        
        // Find best quotes
        auto best_bid = std::max_element(quotes.begin(), quotes.end(),
            [](const QuoteData& a, const QuoteData& b) {
                return a.bid_prices[0] < b.bid_prices[0];
            });
        
        auto best_ask = std::min_element(quotes.begin(), quotes.end(),
            [](const QuoteData& a, const QuoteData& b) {
                return a.ask_prices[0] < b.ask_prices[0];
            });
        
        // Calculate average spread
        double total_spread = 0.0;
        for (const auto& quote : quotes) {
            total_spread += (quote.ask_prices[0] - quote.bid_prices[0]);
        }
        double avg_spread = total_spread / quotes.size();
        
        auto processing_time = std::chrono::high_resolution_clock::now();
        
        auto creation_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            creation_time - start_time).count();
        auto processing_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            processing_time - creation_time).count();
        
        // Verify results
        REQUIRE(quotes.size() == num_quotes);
        REQUIRE(best_bid != quotes.end());
        REQUIRE(best_ask != quotes.end());
        REQUIRE(avg_spread > 0.0);
        
        // Performance assertions
        REQUIRE(creation_duration < 2000);    // Should create 50k quotes in less than 2 seconds
        REQUIRE(processing_duration < 200);   // Should process 50k quotes in less than 200ms
        
        INFO("Created " << num_quotes << " quotes in " << creation_duration << "ms");
        INFO("Processed " << num_quotes << " quotes in " << processing_duration << "ms");
        INFO("Average spread: " << avg_spread);
    }
}

TEST_CASE("Performance - Memory usage", "[performance]") {
    SECTION("Memory efficiency of data structures") {
        // Test memory usage of different data structures
        const size_t num_items = 1000;
        
        // Test BarData memory usage
        std::vector<BarData> bars;
        bars.reserve(num_items);
        
        for (size_t i = 0; i < num_items; ++i) {
            BarData bar("TEST", now(), BarSize::Minute1, BarType::Candle);
            bars.push_back(bar);
        }
        
        // Test QuoteData memory usage
        std::vector<QuoteData> quotes;
        quotes.reserve(num_items);
        
        for (size_t i = 0; i < num_items; ++i) {
            QuoteData quote;
            quote.symbol = "TEST";
            quotes.push_back(quote);
        }
        
        // Basic size checks
        REQUIRE(sizeof(BarData) > 0);
        REQUIRE(sizeof(QuoteData) > 0);
        REQUIRE(bars.size() == num_items);
        REQUIRE(quotes.size() == num_items);
        
        // Memory should be reasonable (these are rough estimates)
        size_t bar_memory = sizeof(BarData) * num_items;
        size_t quote_memory = sizeof(QuoteData) * num_items;
        
        INFO("BarData size: " << sizeof(BarData) << " bytes");
        INFO("QuoteData size: " << sizeof(QuoteData) << " bytes");
        INFO("Total bar memory for " << num_items << " items: " << bar_memory << " bytes");
        INFO("Total quote memory for " << num_items << " items: " << quote_memory << " bytes");
        
        // Reasonable memory usage (less than 1MB for 1000 items each)
        REQUIRE(bar_memory < 1024 * 1024);
        REQUIRE(quote_memory < 1024 * 1024);
    }
}

TEST_CASE("Performance - Concurrent operations", "[performance]") {
    SECTION("Thread-safe operations") {
        const size_t num_threads = 4;
        const size_t items_per_thread = 1000;
        
        std::vector<std::thread> threads;
        std::vector<std::vector<BarData>> results(num_threads);
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // Create multiple threads that create bar data
        for (size_t t = 0; t < num_threads; ++t) {
            threads.emplace_back([&results, t, items_per_thread]() {
                results[t].reserve(items_per_thread);
                
                for (size_t i = 0; i < items_per_thread; ++i) {
                    BarData bar("THREAD_" + std::to_string(t), now(), BarSize::Minute1, BarType::Candle);
                    bar.open = 10.0 + t;
                    bar.close = bar.open + 0.1;
                    bar.volume = 1000 + i;
                    results[t].push_back(bar);
                }
            });
        }
        
        // Wait for all threads to complete
        for (auto& thread : threads) {
            thread.join();
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time).count();
        
        // Verify results
        size_t total_items = 0;
        for (const auto& result : results) {
            REQUIRE(result.size() == items_per_thread);
            total_items += result.size();
        }
        
        REQUIRE(total_items == num_threads * items_per_thread);
        
        // Performance assertion
        REQUIRE(duration < 5000);  // Should complete in less than 5 seconds
        
        INFO("Created " << total_items << " items across " << num_threads << " threads in " << duration << "ms");
    }
}
