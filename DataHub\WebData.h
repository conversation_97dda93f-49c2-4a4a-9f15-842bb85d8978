#pragma once

// http://hq.sinajs.cn/list=sh000001,sz399001,sz000056,sh601857,sh600036,hk00358
//名称,今开,昨收,现价,最高,最低,买入价,卖出价,总量,总金额,买量一,买价一,买量二,买价二,买量三,买价三,买量四,买价四,买量五,买价五,卖量一,卖价一,卖量二,卖价二,卖量三,卖价三,卖量四,卖价四,卖量五,卖价五,更新日期,更新时间
// http://hq.sinajs.cn/list=CFF_IF1101,A1101,AU1101,L1102,CF1101
// http://hq.sinajs.cn/list=hf_IF1101,A1101,AU1101,L1102,CF1101
// http://hq.sinajs.cn/list=int_dji
// http://hq.sinajs.cn/list=int_nasdaq
// http://hq.sinajs.cn/list=b_TWSE
// http://hq.sinajs.cn/list=b_FSSTI
// http://hq.sinajs.cn/list=s_sh000001
//国内指数和商品
// http://hq.sinajs.cn/list=s_sh000001,s_sz399001,s_sh000300,s_sz395099
//金融期指
// http://hq.sinajs.cn/list=CFF_IF1101
//外汇
// http://hq.sinajs.cn/list=HKD,AUD,CAD,CHF,EUR,GBP,JPY,DINI
//国际商品:
// http://hq.sinajs.cn/list=hf_NID
//
// 1.获取交易日
// http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.GetTradeDays?symbol=sh000300&start=2008-12-29
//
// 2.获取5分钟数据
//股票
// http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData?symbol=sz159901&scale=5&ma=no&datalen=48

//期货分时数据
// http://stock2.finance.sina.com.cn/futures/api/json.php/CffexFuturesService.getCffexFuturesMinLine?symbol=IF1408
// http://stock2.finance.sina.com.cn/futures/api/json.php/CffexFuturesService.getCffexFuturesMinLine5d?symbol=IF1408

// IF KLine数据
// http://stock2.finance.sina.com.cn/futures/api/json.php/CffexFuturesService.getCffexFuturesMiniKLine5m?symbol=IF1408
// http://stock2.finance.sina.com.cn/futures/api/json.php/CffexFuturesService.getCffexFuturesDailyKLine?symbol=IF1409

//商品期货KLine数据
// http://stock2.finance.sina.com.cn/futures/api/json.php/InnerFuturesService.getInnerFutures5MinKLine?symbol=AG1610
// http://stock2.finance.sina.com.cn/futures/api/json.php/InnerFuturesService.getInnerFutures5MinKLine?symbol=AU1412
// http://stock2.finance.sina.com.cn/futures/api/json.php/IndexService.getInnerFuturesDailyKLine?symbol=RB1501
// https://stock2.finance.sina.com.cn/futures/api/jsonp.php/var%20_RB20052020_2_28=/InnerFuturesNewService.getDailyKLine?symbol=EB2005&_=2020_2_28

// http://stock2.finance.sina.com.cn/futures/api/json.php/IndexService.getInnerFuturesMiniKLine5m?symbol=A1501

// http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData?symbol=sz159901&scale=5&ma=no&datalen=48
//
// http://hq.sinajs.cn/?_=1287978621359&list=s_sh000001,s_sz399001,CFF_RE_IF1011,r_HSI,int_dji,s_sh600000,s_sz000725,s_sz000157,s_sh600031,s_sh601899,s_sh600348,s_sh600030,s_sh600998,s_sz000983,s_sz000002,s_sh600874,s_sh601158,s_sh600018,s_sh600832,s_sh601998,s_sh600963,s_sh600642,s_sh600153,s_sz000402
//
// http://money.finance.sina.com.cn/quotes_service/view/CN_TransListV2.php?num=6&symbol=sh600210
//
// http://finance.sina.com.cn/realstock/company/sh000001/hisdata/klc_cm.js?dya=2011-12-29
// http://finance.sina.com.cn/realstock/company/sh000001/hisdata/klc_kl.js?dya=2011-12-29
//
// http://hq.sinajs.cn/?_=1287978619687&list=new_ysbz_changepercent_up
// http://hq.sinajs.cn/?_=1287978619593&list=sh600210
// http://hq.sinajs.cn/?_=1287978621359&list=s_sh000001,s_sz399001,CFF_RE_IF1011,r_HSI,int_dji,s_sh600000,s_sz000725,s_sz000157,s_sh600031,s_sh601899,s_sh600348,s_sh600030,s_sh600998,s_sz000983,s_sz000002,s_sh600874,s_sh601158,s_sh600018,s_sh600832,s_sh601998,s_sh600963,s_sh600642,s_sh600153,s_sz000402
// http://money.finance.sina.com.cn/crossdomain.xml
// http://money.finance.sina.com.cn/quotes_service/view/CN_TransListV2.php?num=6&symbol=sh600210
// http://pagead2.googlesyndication.com/pagead/gen_204?id=osd&url=http%3A%2F%2Ffinance.sina.com.cn%2Frealstock%2Fcompany%2Fsh600210%2Fnc.shtml&referrer=http%3A%2F%2Fwww.baidu.com%2Fs%3Fbs%3D%25BA%25A3%25B8%25BB%25CD%25A8%25BE%25AB%25D1%25A1%26f%3D8%26wd%3D600210&correlator=1287978608546&eid=36815004&tt=78&deb=1-1-0-0-0-0&r=p
// http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.GetTradeDays?symbol=sh000001&start=2007-10-25
// http://counter.sina.com.cn/time?fm=JS&rn=12879786238437457813496260643
// http://biz.finance.sina.com.cn/stock/industry_up_js.php?limit=12
// http://finance.sina.com.cn/realstock/company/sh600210/iframe/alphabarex1_mgsy.html
// http://hq.sinajs.cn/_=1287978623937&format=text&func=;SINACALLBACKFLOW%28zjlx_paihang%29;&list=zjlx_paihang
// http://finance.sina.com.cn/realstock/company/sh600210/iframe/pie3d_csimex1_industry.html
// http://mark.sina.com.cn/v2/GetAllResult.php?p_mark=gp&i_mark=sh600210
// http://finance.sina.com.cn/realstock/company/sh600210/iframe/bar_csimex2.html
// http://hq.sinajs.cn/?_=1287978625250&list=sh600210
// http://hq.sinajs.cn/random=128797862523414933453286215648&list=market_stock_sh

// http://data.quotes.money.163.com/?0000001;1399001;1000056;0601857;0600036
// http://stock.business.sohu.com/p/pl.php?code=1A0001,399001,000056,601857,600036
//

//腾讯
// http://qt.gtimg.cn/r=0.08732812595553696q=sh000001,sz399001,r_hkHSI
// http://qt.gtimg.cn/q=sh600186,sz002604,sz300290,sz000032,sz002438,sz002167,sz000679,sz002566,sh600130,sz002667,sz002761,sz300179,sz000705,sh600215,sz300312,sz300103,sz002662,sh600158,sh600085,sh600088,sz300377,sz002733,sz300426,sz300328,sz002709&m=push&r=*********
// http://qt.gtimg.cn/q=sz300463,sz300462,sz300461,sz300460,sz300459,sz300458,sz300457,sz300456,sz300455,sz300453,sz300452,sz300451,sz300450,sz300449,sz300448,sz300447,sz300446,sz300445,sz300444,sz300443,sz300442,sz300441,sz300440,sz300439,sz300438&m=push&r=*********
// http://qt.gtimg.cn/q=sz300498,sz300489,sz300488,sz300487,sz300486,sz300485,sz300483,sz300482,sz300481,sz300480,sz300479,sz300478,sz300477,sz300476,sz300475,sz300473,sz300472,sz300471,sz300470,sz300469,sz300468,sz300467,sz300466,sz300465,sz300464&m=push&r=86715118
#include <DataHub.h>

#include <boost/function.hpp>
#include <boost/thread.hpp>
#include <vector>
#include <boost/pool/object_pool.hpp>

#include "../utils/httpclient.h"
namespace dal {
class WebData {
 public:
  WebData(void);
  ~WebData(void);

  void Init(DataHub* DataHubPtr, int version = 0);
  void Start();
  void Stop();
  void GetNewQuoteData(const std::string& label);
  HistoryDataSeqPtr GetHistoryData(const std::string& label,
                                   const int barsize = 250,
                                   BarSize barperiod = BarSize::day);

 private:
  long fresh_custom_quote_links();
  long fresh_all_quote_links();
  void read_stock_quote(std::string quote);
  void read_sina_stock_quote_split(std::string quote);

  void read_tencent_stock_quote_split(std::string quote);
  void read_sina_future_quote_split(std::string quote);
  void read_sina_kline_data(std::string& kldata, HistoryDataSeqPtr hds_ptr);
  void read_sina_futrue_kline_data(std::string& kldata,
                                   HistoryDataSeqPtr hds_ptr);
  void read_sina_futrue_kline_data_split(std::string& kldata,
                                         HistoryDataSeqPtr hds_ptr);

  void read_sina_future_kline_json_data(std::string& kldata, HistoryDataSeqPtr hds_ptr);

  //获取市场所有证券最新行情数据
  void StartAllBlk();
  void StopAllBlk();
  void RunAllBlk();

  inline std::string to_sina_label(std::string label);
  inline std::string get_sina_label(const std::string& line);
  inline std::string get_tencent_label(const std::string& line);

  std::string m_sUrl;

  size_t m_count;
  bool m_flag;
  int m_nVersion;

  DataHub* m_DataHubPtr;
  tp_multithread::tasks_processor* _task_pro;
  std::vector<std::string> _links;

  boost::thread _thdCustomblk;
  boost::thread _thdAllblk;

  bool _bStopThreadCustom;
  bool _bStopThreadAll;

  CHttpClient _httpClient;

  unsigned int _update_count;
  //boost::object_pool<QuoteData> _qd_pool; //使用内存池来管理QuoteData对象，减少内存分配和释放的开销
};
}  // namespace dal
