/**
 * @file test_integration.cpp
 * @brief Integration tests for QuantServices
 * <AUTHOR> Team
 * @date 2024
 */

#include <gtest/gtest.h>
#include "quantservices/QuantServices.h"
#include <thread>
#include <chrono>

using namespace QuantServices;

class QuantServicesIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test configuration
        config_.server_host = "127.0.0.1";
        config_.server_port = 18080; // Use different port for testing
        config_.worker_threads = 2;
        config_.enable_datahub = true;
        config_.enable_trading = true;
        config_.log_level = "debug";
        config_.log_to_console = false;
        config_.log_file = "logs/test.log";
        
        // Create application
        app_ = std::make_unique<QuantServicesApp>(config_);
    }
    
    void TearDown() override {
        if (app_) {
            app_->stop();
        }
    }
    
    ServiceConfig config_;
    std::unique_ptr<QuantServicesApp> app_;
};

TEST_F(QuantServicesIntegrationTest, ApplicationLifecycle) {
    // Test initialization
    ASSERT_TRUE(app_->initialize());
    EXPECT_EQ(app_->get_status(), ServiceStatus::Stopped);
    
    // Test starting
    ASSERT_TRUE(app_->start());
    EXPECT_EQ(app_->get_status(), ServiceStatus::Running);
    
    // Wait a bit for services to stabilize
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Test that services are accessible
    auto service_manager = app_->get_service_manager();
    ASSERT_NE(service_manager, nullptr);
    
    auto api_server = app_->get_api_server();
    ASSERT_NE(api_server, nullptr);
    EXPECT_TRUE(api_server->is_running());
    
    // Test stopping
    app_->stop();
    EXPECT_EQ(app_->get_status(), ServiceStatus::Stopped);
}

TEST_F(QuantServicesIntegrationTest, ConfigurationManagement) {
    ASSERT_TRUE(app_->initialize());
    
    // Test getting configuration
    const auto& config = app_->get_config();
    EXPECT_EQ(config.server_host, "127.0.0.1");
    EXPECT_EQ(config.server_port, 18080);
    EXPECT_TRUE(config.enable_datahub);
    EXPECT_TRUE(config.enable_trading);
    
    // Test updating configuration
    ServiceConfig new_config = config;
    new_config.worker_threads = 4;
    new_config.log_level = "info";
    
    EXPECT_TRUE(app_->update_config(new_config));
    
    const auto& updated_config = app_->get_config();
    EXPECT_EQ(updated_config.worker_threads, 4);
    EXPECT_EQ(updated_config.log_level, "info");
}

TEST_F(QuantServicesIntegrationTest, HealthMonitoring) {
    ASSERT_TRUE(app_->initialize());
    ASSERT_TRUE(app_->start());
    
    // Wait for health monitoring to start
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // Test health status
    auto health_status = app_->get_health_status();
    EXPECT_TRUE(health_status.is_object());
    EXPECT_TRUE(health_status.contains("overall_status"));
    
    // Test system metrics
    auto system_metrics = app_->get_system_metrics();
    EXPECT_TRUE(system_metrics.is_object());
    EXPECT_TRUE(system_metrics.contains("cpu"));
    EXPECT_TRUE(system_metrics.contains("memory"));
    EXPECT_TRUE(system_metrics.contains("timestamp"));
}

TEST_F(QuantServicesIntegrationTest, ServiceManagement) {
    ASSERT_TRUE(app_->initialize());
    ASSERT_TRUE(app_->start());
    
    auto service_manager = app_->get_service_manager();
    ASSERT_NE(service_manager, nullptr);
    
    // Test service status
    auto services_status = service_manager->get_all_services_status();
    EXPECT_FALSE(services_status.empty());
    
    // Check that expected services are present
    EXPECT_TRUE(services_status.find("datahub") != services_status.end());
    EXPECT_TRUE(services_status.find("trading") != services_status.end());
    
    // Test individual service status
    auto datahub_status = service_manager->get_service_status("datahub");
    EXPECT_NE(datahub_status, ServiceStatus::Error);
    
    auto trading_status = service_manager->get_service_status("trading");
    EXPECT_NE(trading_status, ServiceStatus::Error);
}

TEST_F(QuantServicesIntegrationTest, ApiServerFunctionality) {
    ASSERT_TRUE(app_->initialize());
    ASSERT_TRUE(app_->start());
    
    auto api_server = app_->get_api_server();
    ASSERT_NE(api_server, nullptr);
    
    // Test server is running
    EXPECT_TRUE(api_server->is_running());
    
    // Test server configuration
    const auto& server_config = api_server->get_config();
    EXPECT_EQ(server_config.host, "127.0.0.1");
    EXPECT_EQ(server_config.port, 18080);
    EXPECT_EQ(server_config.thread_count, 2);
    
    // Test server metrics
    auto metrics = api_server->get_metrics();
    EXPECT_TRUE(metrics.is_object());
    EXPECT_TRUE(metrics.contains("total_requests"));
    EXPECT_TRUE(metrics.contains("running"));
    EXPECT_TRUE(metrics["running"].get<bool>());
}

// Factory function tests
TEST(QuantServicesFactoryTest, CreateDefaultApp) {
    auto app = Factory::create_default_app(19080);
    ASSERT_NE(app, nullptr);
    
    const auto& config = app->get_config();
    EXPECT_EQ(config.server_port, 19080);
    EXPECT_EQ(config.server_host, "0.0.0.0"); // Default host
}

TEST(QuantServicesFactoryTest, CreateAppWithConfig) {
    ServiceConfig config;
    config.server_host = "localhost";
    config.server_port = 19081;
    config.enable_datahub = false;
    config.enable_trading = true;
    
    auto app = Factory::create_app_with_config(config);
    ASSERT_NE(app, nullptr);
    
    const auto& app_config = app->get_config();
    EXPECT_EQ(app_config.server_host, "localhost");
    EXPECT_EQ(app_config.server_port, 19081);
    EXPECT_FALSE(app_config.enable_datahub);
    EXPECT_TRUE(app_config.enable_trading);
}

// Utility function tests
TEST(QuantServicesUtilsTest, TimestampFunctions) {
    // Test current timestamp
    auto timestamp = Utils::get_current_timestamp();
    EXPECT_FALSE(timestamp.empty());
    EXPECT_TRUE(timestamp.find("T") != std::string::npos); // ISO format
    EXPECT_TRUE(timestamp.find("Z") != std::string::npos); // UTC indicator
    
    // Test timestamp parsing
    std::string test_timestamp = "2024-01-01T12:00:00";
    auto parsed_time = Utils::parse_timestamp(test_timestamp);
    EXPECT_NE(parsed_time, TimePoint{});
}

TEST(QuantServicesUtilsTest, UuidGeneration) {
    auto uuid1 = Utils::generate_uuid();
    auto uuid2 = Utils::generate_uuid();
    
    EXPECT_FALSE(uuid1.empty());
    EXPECT_FALSE(uuid2.empty());
    EXPECT_NE(uuid1, uuid2); // Should be different
    EXPECT_EQ(uuid1.length(), 36); // Standard UUID length with hyphens
}

TEST(QuantServicesUtilsTest, JsonResponseHelpers) {
    // Test success response
    Json data = {{"key", "value"}};
    auto success_response = Utils::create_success_response(data);
    
    EXPECT_TRUE(success_response["success"].get<bool>());
    EXPECT_EQ(success_response["data"], data);
    EXPECT_TRUE(success_response.contains("timestamp"));
    
    // Test error response
    auto error_response = Utils::create_error_response("Test error", "Test details");
    
    EXPECT_FALSE(error_response["success"].get<bool>());
    EXPECT_EQ(error_response["error"]["message"], "Test error");
    EXPECT_EQ(error_response["error"]["details"], "Test details");
    EXPECT_TRUE(error_response.contains("timestamp"));
}

TEST(QuantServicesUtilsTest, JsonSchemaValidation) {
    Json schema = {
        {"type", "object"},
        {"required", Json::array({"name", "age"})},
        {"properties", {
            {"name", {{"type", "string"}}},
            {"age", {{"type", "number"}}}
        }}
    };
    
    // Valid data
    Json valid_data = {
        {"name", "John"},
        {"age", 30}
    };
    EXPECT_TRUE(Utils::validate_json_schema(valid_data, schema));
    
    // Invalid data (missing required field)
    Json invalid_data = {
        {"name", "John"}
    };
    EXPECT_FALSE(Utils::validate_json_schema(invalid_data, schema));
    
    // Invalid data (wrong type)
    Json wrong_type_data = {
        {"name", "John"},
        {"age", "thirty"}
    };
    EXPECT_FALSE(Utils::validate_json_schema(wrong_type_data, schema));
}

// Performance test
TEST(QuantServicesPerformanceTest, DISABLED_StartupTime) {
    ServiceConfig config;
    config.server_port = 19082;
    config.log_to_console = false;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    auto app = Factory::create_app_with_config(config);
    ASSERT_TRUE(app->initialize());
    ASSERT_TRUE(app->start());
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Startup should be reasonably fast (less than 5 seconds)
    EXPECT_LT(duration.count(), 5000);
    
    app->stop();
    
    std::cout << "Startup time: " << duration.count() << " ms" << std::endl;
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
