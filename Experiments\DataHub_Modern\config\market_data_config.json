{"version": "1.0", "service": {"name": "DataHub_Modern_MarketDataService", "enable_logging": true, "enable_metrics": true, "enable_rest_api": false, "rest_api_port": 8080, "rest_api_host": "0.0.0.0"}, "providers": {"ctp": {"enabled": false, "front_address": "tcp://***************:10131", "broker_id": "9999", "user_id": "your_user_id", "password": "your_password", "flow_path": "./ctp_flow/", "using_udp": false, "multicast": false, "buffer_size": 64, "auto_reconnect": true, "reconnect_interval": 30, "heartbeat_timeout": 60}, "webdata": {"enabled": true, "data_source": "sina", "base_url": "", "update_interval": 3000, "request_timeout": 5000, "max_symbols_per_request": 100, "max_concurrent_requests": 5, "enable_cache": true, "cache_ttl": 30, "only_update_subscribed": true, "user_agent": "DataHub/1.0", "headers": {"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3", "Accept-Encoding": "gzip, deflate", "Connection": "keep-alive"}}}, "manager": {"routing_strategy": "primary", "provider_priorities": [{"type": "ctp_futures", "priority": 1, "is_primary": true, "is_backup": false}, {"type": "webdata_stock", "priority": 2, "is_primary": false, "is_backup": true}], "enable_failover": true, "failover_timeout": 30, "enable_data_validation": true, "enable_duplicate_detection": true, "duplicate_window": 1000, "max_cache_size": 10000, "cache_ttl": 300}, "logging": {"level": "info", "file_path": "./logs/market_data.log", "max_file_size": "100MB", "max_files": 10, "console_output": true, "file_output": true, "format": "[%Y-%m-%d %H:%M:%S.%f] [%l] [%n] %v"}, "metrics": {"enabled": true, "collection_interval": 60, "export_format": "prometheus", "export_endpoint": "/metrics", "custom_metrics": {"quote_latency_histogram": true, "provider_status_gauge": true, "error_rate_counter": true, "cache_hit_ratio_gauge": true}}, "security": {"enable_encryption": false, "encryption_key": "", "enable_authentication": false, "api_keys": [], "rate_limiting": {"enabled": false, "requests_per_minute": 1000, "burst_size": 100}}, "performance": {"thread_pool_size": 4, "io_thread_count": 2, "max_concurrent_requests": 100, "request_queue_size": 1000, "enable_connection_pooling": true, "connection_pool_size": 10, "connection_timeout": 30, "read_timeout": 10}, "data_sources": {"sina": {"quote_url": "http://hq.sinajs.cn/list=", "history_url": "http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData", "rate_limit": 100, "retry_count": 3, "retry_delay": 1000}, "tencent": {"quote_url": "http://qt.gtimg.cn/q=", "history_url": "http://web.ifzq.gtimg.cn/appstock/app/fqkline/get", "rate_limit": 100, "retry_count": 3, "retry_delay": 1000}, "netease": {"quote_url": "http://api.money.126.net/data/feed/", "history_url": "http://quotes.money.163.com/service/chddata.html", "rate_limit": 50, "retry_count": 3, "retry_delay": 1000}}, "symbols": {"default_subscriptions": ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH"], "symbol_mapping": {"000001.SZ": "sz000001", "000002.SZ": "sz000002", "600000.SH": "sh600000", "600036.SH": "sh600036"}, "market_hours": {"SZ": {"morning_start": "09:30:00", "morning_end": "11:30:00", "afternoon_start": "13:00:00", "afternoon_end": "15:00:00", "timezone": "Asia/Shanghai"}, "SH": {"morning_start": "09:30:00", "morning_end": "11:30:00", "afternoon_start": "13:00:00", "afternoon_end": "15:00:00", "timezone": "Asia/Shanghai"}}}, "alerts": {"enabled": false, "price_change_threshold": 5.0, "volume_spike_threshold": 200.0, "connection_failure_alert": true, "data_delay_threshold": 60, "notification_channels": {"email": {"enabled": false, "smtp_server": "", "smtp_port": 587, "username": "", "password": "", "recipients": []}, "webhook": {"enabled": false, "url": "", "headers": {}, "timeout": 5}}}, "development": {"mock_mode": false, "mock_data_file": "./test_data/mock_quotes.json", "debug_mode": false, "profiling_enabled": false, "test_symbols": ["TEST001.SZ", "TEST002.SH"]}}