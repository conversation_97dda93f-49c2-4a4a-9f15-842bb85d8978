/**
 * @file basic_usage.cpp
 * @brief Basic usage example for QuantServices
 * <AUTHOR> Team
 * @date 2024
 */

#include "quantservices/QuantServices.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace QuantServices;

int main() {
    try {
        std::cout << "=== QuantServices Basic Usage Example ===" << std::endl;
        
        // 1. Create configuration
        ServiceConfig config;
        config.server_host = "127.0.0.1";
        config.server_port = 8080;
        config.worker_threads = 2;
        config.enable_datahub = true;
        config.enable_trading = true;
        config.log_level = "info";
        config.log_to_console = true;
        
        std::cout << "Configuration created:" << std::endl;
        std::cout << "  Host: " << config.server_host << std::endl;
        std::cout << "  Port: " << config.server_port << std::endl;
        std::cout << "  DataHub: " << (config.enable_datahub ? "Enabled" : "Disabled") << std::endl;
        std::cout << "  Trading: " << (config.enable_trading ? "Enabled" : "Disabled") << std::endl;
        std::cout << std::endl;
        
        // 2. Create QuantServices application
        auto app = Factory::create_app_with_config(config);
        if (!app) {
            std::cerr << "Failed to create QuantServices application" << std::endl;
            return 1;
        }
        
        std::cout << "QuantServices application created successfully" << std::endl;
        
        // 3. Initialize the application
        if (!app->initialize()) {
            std::cerr << "Failed to initialize QuantServices application" << std::endl;
            return 1;
        }
        
        std::cout << "Application initialized successfully" << std::endl;
        
        // 4. Start the application
        if (!app->start()) {
            std::cerr << "Failed to start QuantServices application" << std::endl;
            return 1;
        }
        
        std::cout << "Application started successfully!" << std::endl;
        std::cout << "API available at: http://" << config.server_host << ":" << config.server_port << "/api/v1" << std::endl;
        std::cout << std::endl;
        
        // 5. Display service status
        auto service_manager = app->get_service_manager();
        if (service_manager) {
            auto services_status = service_manager->get_all_services_status();
            
            std::cout << "Service Status:" << std::endl;
            for (const auto& [name, info] : services_status) {
                std::cout << "  " << name << ": " << static_cast<int>(info.status) << std::endl;
            }
            std::cout << std::endl;
        }
        
        // 6. Display health status
        auto health_status = app->get_health_status();
        std::cout << "Health Status:" << std::endl;
        std::cout << health_status.dump(2) << std::endl;
        std::cout << std::endl;
        
        // 7. Display system metrics
        auto system_metrics = app->get_system_metrics();
        std::cout << "System Metrics:" << std::endl;
        std::cout << system_metrics.dump(2) << std::endl;
        std::cout << std::endl;
        
        // 8. Run for a short time to demonstrate
        std::cout << "Running for 10 seconds..." << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(10));
        
        // 9. Stop the application
        std::cout << "Stopping application..." << std::endl;
        app->stop();
        
        std::cout << "Application stopped successfully" << std::endl;
        std::cout << "Example completed!" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown exception occurred" << std::endl;
        return 1;
    }
}
