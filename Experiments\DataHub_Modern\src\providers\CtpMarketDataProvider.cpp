#include "providers/CtpMarketDataProvider.h"
#include "core/Logging.h"
#include <algorithm>
#include <sstream>
#include <iomanip>

// CTP API头文件
#ifdef _WIN32
#include <CTP/ThostFtdcMdApi.h>
#else
// Linux版本的CTP API头文件路径可能不同
#include <ThostFtdcMdApi.h>
#endif

namespace DataHub::Providers {

// CTP回调处理器实现
class CtpMdSpiImpl : public CThostFtdcMdSpi {
public:
    explicit CtpMdSpiImpl(CtpMarketDataProvider* provider) : provider_(provider) {}
    
    void OnFrontConnected() override {
        Core::log_info("CTP", "Front connected");
        provider_->update_connection_status(ConnectionStatus::Connected);
        
        // 发送登录请求
        CThostFtdcReqUserLoginField login_req = {};
        auto config = provider_->ctp_config_;
        
        strncpy_s(login_req.Broker<PERSON>, config->broker_id.c_str(), sizeof(login_req.BrokerID) - 1);
        strncpy_s(login_req.UserID, config->user_id.c_str(), sizeof(login_req.UserID) - 1);
        strncpy_s(login_req.Password, config->password.c_str(), sizeof(login_req.Password) - 1);
        
        int result = provider_->md_api_->ReqUserLogin(&login_req, provider_->get_next_request_id());
        if (result != 0) {
            provider_->handle_error("ReqUserLogin", nullptr);
        }
    }
    
    void OnFrontDisconnected(int reason) override {
        std::string reason_str = get_disconnect_reason(reason);
        Core::log_warn("CTP", "Front disconnected: {}", reason_str);
        
        provider_->update_connection_status(ConnectionStatus::Disconnected, reason_str);
        provider_->logged_in_ = false;
        
        // 如果启用自动重连，尝试重连
        if (provider_->config_->auto_reconnect) {
            // 这里可以实现重连逻辑
        }
    }
    
    void OnHeartBeatWarning(int time_lapse) override {
        Core::log_warn("CTP", "Heartbeat warning: {} seconds", time_lapse);
    }
    
    void OnRspUserLogin(CThostFtdcRspUserLoginField* login_field,
                       CThostFtdcRspInfoField* error_info,
                       int request_id, bool is_last) override {
        if (provider_->is_error_response(error_info)) {
            provider_->handle_error("OnRspUserLogin", error_info);
            return;
        }
        
        Core::log_info("CTP", "User login successful");
        provider_->logged_in_ = true;
        
        if (login_field) {
            Core::log_info("CTP", "Trading day: {}", login_field->TradingDay);
        }
    }
    
    void OnRspUserLogout(CThostFtdcUserLogoutField* logout_field,
                        CThostFtdcRspInfoField* error_info,
                        int request_id, bool is_last) override {
        if (provider_->is_error_response(error_info)) {
            provider_->handle_error("OnRspUserLogout", error_info);
            return;
        }
        
        Core::log_info("CTP", "User logout successful");
        provider_->logged_in_ = false;
    }
    
    void OnRspError(CThostFtdcRspInfoField* error_info,
                   int request_id, bool is_last) override {
        provider_->handle_error("OnRspError", error_info);
    }
    
    void OnRspSubMarketData(CThostFtdcSpecificInstrumentField* instrument,
                           CThostFtdcRspInfoField* error_info,
                           int request_id, bool is_last) override {
        if (provider_->is_error_response(error_info)) {
            provider_->handle_error("OnRspSubMarketData", error_info);
            return;
        }
        
        if (instrument) {
            std::string instrument_id = instrument->InstrumentID;
            Core::Symbol symbol = provider_->instrument_id_to_symbol(instrument_id);
            
            Core::log_info("CTP", "Subscribe market data successful: {}", symbol);
            provider_->update_subscription_status(symbol, SubscriptionStatus::Subscribed);
        }
    }
    
    void OnRspUnSubMarketData(CThostFtdcSpecificInstrumentField* instrument,
                             CThostFtdcRspInfoField* error_info,
                             int request_id, bool is_last) override {
        if (provider_->is_error_response(error_info)) {
            provider_->handle_error("OnRspUnSubMarketData", error_info);
            return;
        }
        
        if (instrument) {
            std::string instrument_id = instrument->InstrumentID;
            Core::Symbol symbol = provider_->instrument_id_to_symbol(instrument_id);
            
            Core::log_info("CTP", "Unsubscribe market data successful: {}", symbol);
            provider_->update_subscription_status(symbol, SubscriptionStatus::Unsubscribed);
        }
    }
    
    void OnRtnDepthMarketData(CThostFtdcDepthMarketDataField* market_data) override {
        if (!market_data) return;
        
        // 将市场数据放入队列，由专门的线程处理
        {
            std::lock_guard<std::mutex> lock(provider_->queue_mutex_);
            provider_->market_data_queue_.push(*market_data);
        }
        provider_->queue_cv_.notify_one();
    }

private:
    CtpMarketDataProvider* provider_;
    
    std::string get_disconnect_reason(int reason) const {
        switch (reason) {
            case 0x1001: return "Network read failed";
            case 0x1002: return "Network write failed";
            case 0x2001: return "Heartbeat timeout";
            case 0x2002: return "Send heartbeat failed";
            case 0x2003: return "Received error message";
            default: return "Unknown reason: " + std::to_string(reason);
        }
    }
};

// CtpMarketDataProvider实现
CtpMarketDataProvider::CtpMarketDataProvider()
    : MarketDataProviderBase(ProviderType::CTP_Futures, "CTP_Futures_Provider")
    , md_api_(nullptr)
    , logged_in_(false)
    , request_id_(0)
    , stop_processing_(false) {
}

CtpMarketDataProvider::~CtpMarketDataProvider() {
    stop();
    
    if (md_api_) {
        md_api_->Release();
        md_api_ = nullptr;
    }
}

Core::Result<void> CtpMarketDataProvider::connect() {
    if (is_connected()) {
        return Core::make_success();
    }
    
    return do_connect();
}

Core::Result<void> CtpMarketDataProvider::disconnect() {
    if (!is_connected()) {
        return Core::make_success();
    }
    
    return do_disconnect();
}

Core::Result<void> CtpMarketDataProvider::do_connect() {
    if (!ctp_config_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "CTP config not set");
    }
    
    try {
        // 创建CTP API实例
        md_api_ = CThostFtdcMdApi::CreateFtdcMdApi(ctp_config_->flow_path.c_str());
        if (!md_api_) {
            return Core::make_error<void>(Core::ErrorCode::InitializationFailed, 
                "Failed to create CTP MD API");
        }
        
        // 创建回调处理器
        md_spi_ = std::make_unique<CtpMdSpiImpl>(this);
        
        // 注册回调
        md_api_->RegisterSpi(md_spi_.get());
        
        // 设置UDP缓冲区大小
        if (ctp_config_->using_udp) {
            md_api_->SetUDPBufferSize(ctp_config_->buffer_size);
        }
        
        // 注册前置地址
        char* front_addr = const_cast<char*>(ctp_config_->front_address.c_str());
        md_api_->RegisterFront(front_addr);
        
        // 初始化API
        md_api_->Init();
        
        update_connection_status(ConnectionStatus::Connecting);
        
        Core::log_info("CTP", "Connecting to front: {}", ctp_config_->front_address);
        
        return Core::make_success();
        
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::ConnectionFailed, 
            "CTP connect failed: " + std::string(e.what()));
    }
}

Core::Result<void> CtpMarketDataProvider::do_disconnect() {
    try {
        if (md_api_) {
            // 发送登出请求
            if (logged_in_) {
                CThostFtdcUserLogoutField logout_req = {};
                auto config = ctp_config_;
                
                strncpy_s(logout_req.BrokerID, config->broker_id.c_str(), 
                         sizeof(logout_req.BrokerID) - 1);
                strncpy_s(logout_req.UserID, config->user_id.c_str(), 
                         sizeof(logout_req.UserID) - 1);
                
                md_api_->ReqUserLogout(&logout_req, get_next_request_id());
            }
            
            // 释放API
            md_api_->Release();
            md_api_ = nullptr;
        }
        
        md_spi_.reset();
        logged_in_ = false;
        
        update_connection_status(ConnectionStatus::Disconnected);
        
        Core::log_info("CTP", "Disconnected");
        
        return Core::make_success();
        
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::DisconnectionFailed,
            "CTP disconnect failed: " + std::string(e.what()));
    }
}

Core::Result<void> CtpMarketDataProvider::do_start() {
    start_data_processing();
    return Core::make_success();
}

Core::Result<void> CtpMarketDataProvider::do_stop() {
    stop_data_processing();
    return Core::make_success();
}

void CtpMarketDataProvider::start_data_processing() {
    stop_processing_ = false;
    
    // 启动数据处理线程
    data_processing_thread_ = std::thread([this]() {
        process_market_data();
    });
    
    // 启动Tick数据处理线程
    tick_processing_thread_ = std::thread([this]() {
        process_tick_data();
    });
}

void CtpMarketDataProvider::stop_data_processing() {
    stop_processing_ = true;
    queue_cv_.notify_all();
    
    if (data_processing_thread_.joinable()) {
        data_processing_thread_.join();
    }
    
    if (tick_processing_thread_.joinable()) {
        tick_processing_thread_.join();
    }
}

int CtpMarketDataProvider::get_next_request_id() {
    return ++request_id_;
}

void CtpMarketDataProvider::process_market_data() {
    Core::log_info("CTP", "Market data processing thread started");

    while (!stop_processing_) {
        std::unique_lock<std::mutex> lock(queue_mutex_);

        // 等待数据或停止信号
        queue_cv_.wait(lock, [this]() {
            return !market_data_queue_.empty() || stop_processing_;
        });

        if (stop_processing_) break;

        // 处理队列中的所有数据
        while (!market_data_queue_.empty()) {
            auto market_data = market_data_queue_.front();
            market_data_queue_.pop();
            lock.unlock();

            try {
                // 转换为QuoteData并通知
                auto quote = convert_market_data(market_data);
                notify_quote(quote);

                // 生成Tick数据
                auto tick = convert_tick_data(market_data);
                notify_tick(tick);

            } catch (const std::exception& e) {
                Core::log_error("CTP", "Error processing market data: {}", e.what());
                notify_error("Market data processing error: " + std::string(e.what()));
            }

            lock.lock();
        }
    }

    Core::log_info("CTP", "Market data processing thread stopped");
}

void CtpMarketDataProvider::process_tick_data() {
    Core::log_info("CTP", "Tick data processing thread started");

    // 这里可以实现更复杂的Tick数据处理逻辑
    // 比如数据聚合、K线生成等

    while (!stop_processing_) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        // TODO: 实现Tick数据处理逻辑
    }

    Core::log_info("CTP", "Tick data processing thread stopped");
}

Core::QuoteData CtpMarketDataProvider::convert_market_data(
    const CThostFtdcDepthMarketDataField& md) const {

    Core::QuoteData quote;

    // 基本信息
    quote.symbol = instrument_id_to_symbol(md.InstrumentID);

    // 时间戳处理
    std::string datetime_str = std::string(md.ActionDay) + " " + std::string(md.UpdateTime);
    if (md.UpdateMillisec > 0) {
        datetime_str += "." + std::to_string(md.UpdateMillisec);
    }

    // 这里需要实现字符串到时间戳的转换
    quote.timestamp = std::chrono::system_clock::now(); // 临时使用当前时间

    // 价格信息
    quote.last_price = md.LastPrice;
    quote.open_price = md.OpenPrice;
    quote.high_price = md.HighestPrice;
    quote.low_price = md.LowestPrice;
    quote.close_price = md.ClosePrice;
    quote.pre_close = md.PreClosePrice;

    // 成交量和成交额
    quote.volume = static_cast<Core::Volume>(md.Volume);
    quote.amount = md.Turnover;

    // 买卖盘信息
    quote.bid_prices[0] = md.BidPrice1;
    quote.bid_volumes[0] = static_cast<Core::Volume>(md.BidVolume1);
    quote.ask_prices[0] = md.AskPrice1;
    quote.ask_volumes[0] = static_cast<Core::Volume>(md.AskVolume1);

    // 涨跌停价格
    if (md.UpperLimitPrice != DBL_MAX) {
        quote.upper_limit = md.UpperLimitPrice;
    }
    if (md.LowerLimitPrice != -DBL_MAX) {
        quote.lower_limit = md.LowerLimitPrice;
    }

    // 期货特有字段
    if (md.SettlementPrice != DBL_MAX) {
        quote.settlement_price = md.SettlementPrice;
    }
    quote.open_interest = static_cast<Core::Volume>(md.OpenInterest);

    return quote;
}

Core::TickData CtpMarketDataProvider::convert_tick_data(
    const CThostFtdcDepthMarketDataField& md) const {

    Core::TickData tick;

    tick.symbol = instrument_id_to_symbol(md.InstrumentID);
    tick.timestamp = std::chrono::system_clock::now(); // 临时使用当前时间
    tick.price = md.LastPrice;
    tick.volume = static_cast<Core::Volume>(md.Volume);
    tick.amount = md.Turnover;

    // 简单的买卖方向判断
    if (md.LastPrice >= md.AskPrice1) {
        tick.direction = Core::TickData::Direction::Buy;
    } else if (md.LastPrice <= md.BidPrice1) {
        tick.direction = Core::TickData::Direction::Sell;
    } else {
        tick.direction = Core::TickData::Direction::Neutral;
    }

    return tick;
}

std::string CtpMarketDataProvider::symbol_to_instrument_id(const Core::Symbol& symbol) const {
    // 简单的符号转换，实际应用中可能需要更复杂的映射
    // 例如：IF2312.CFFEX -> IF2312
    auto pos = symbol.find('.');
    if (pos != std::string::npos) {
        return symbol.substr(0, pos);
    }
    return symbol;
}

Core::Symbol CtpMarketDataProvider::instrument_id_to_symbol(const std::string& instrument_id) const {
    // 简单的符号转换，实际应用中需要根据交易所添加后缀
    // 例如：IF2312 -> IF2312.CFFEX
    // 这里需要根据合约代码判断交易所
    return instrument_id + ".CFFEX"; // 临时实现
}

bool CtpMarketDataProvider::is_error_response(CThostFtdcRspInfoField* error_info) const {
    return error_info && error_info->ErrorID != 0;
}

void CtpMarketDataProvider::handle_error(const std::string& function,
                                        CThostFtdcRspInfoField* error_info) {
    std::string error_msg = function;

    if (error_info) {
        error_msg += " failed: [" + std::to_string(error_info->ErrorID) + "] " +
                    std::string(error_info->ErrorMsg);
    } else {
        error_msg += " failed: Unknown error";
    }

    Core::log_error("CTP", error_msg);
    notify_error(error_msg);
}

// 订阅和查询方法实现
Core::Result<void> CtpMarketDataProvider::subscribe_quote(const Core::Symbol& symbol) {
    return do_subscribe(symbol);
}

Core::Result<void> CtpMarketDataProvider::subscribe_quotes(
    const std::vector<Core::Symbol>& symbols) {

    for (const auto& symbol : symbols) {
        auto result = subscribe_quote(symbol);
        if (!result.is_success()) {
            return result;
        }
    }
    return Core::make_success();
}

Core::Result<void> CtpMarketDataProvider::unsubscribe_quote(const Core::Symbol& symbol) {
    return do_unsubscribe(symbol);
}

Core::Result<void> CtpMarketDataProvider::unsubscribe_all() {
    auto subscriptions = get_subscriptions();

    for (const auto& sub : subscriptions) {
        if (sub.status == SubscriptionStatus::Subscribed) {
            auto result = unsubscribe_quote(sub.symbol);
            if (!result.is_success()) {
                Core::log_warn("CTP", "Failed to unsubscribe {}: {}",
                              sub.symbol, result.error().message());
            }
        }
    }

    return Core::make_success();
}

Core::Result<void> CtpMarketDataProvider::do_subscribe(const Core::Symbol& symbol) {
    if (!md_api_ || !logged_in_) {
        return Core::make_error<void>(Core::ErrorCode::NotConnected,
            "CTP not connected or not logged in");
    }

    try {
        std::string instrument_id = symbol_to_instrument_id(symbol);
        char* instruments[] = { const_cast<char*>(instrument_id.c_str()) };

        update_subscription_status(symbol, SubscriptionStatus::Subscribing);

        int result = md_api_->SubscribeMarketData(instruments, 1);
        if (result != 0) {
            update_subscription_status(symbol, SubscriptionStatus::Failed,
                "Subscribe request failed");
            return Core::make_error<void>(Core::ErrorCode::SubscriptionFailed,
                "CTP SubscribeMarketData failed: " + std::to_string(result));
        }

        Core::log_info("CTP", "Subscribing to market data: {}", symbol);
        return Core::make_success();

    } catch (const std::exception& e) {
        update_subscription_status(symbol, SubscriptionStatus::Failed, e.what());
        return Core::make_error<void>(Core::ErrorCode::SubscriptionFailed,
            "Subscribe failed: " + std::string(e.what()));
    }
}

Core::Result<void> CtpMarketDataProvider::do_unsubscribe(const Core::Symbol& symbol) {
    if (!md_api_) {
        return Core::make_error<void>(Core::ErrorCode::NotConnected, "CTP not connected");
    }

    try {
        std::string instrument_id = symbol_to_instrument_id(symbol);
        char* instruments[] = { const_cast<char*>(instrument_id.c_str()) };

        int result = md_api_->UnSubscribeMarketData(instruments, 1);
        if (result != 0) {
            return Core::make_error<void>(Core::ErrorCode::UnsubscriptionFailed,
                "CTP UnSubscribeMarketData failed: " + std::to_string(result));
        }

        Core::log_info("CTP", "Unsubscribing from market data: {}", symbol);
        return Core::make_success();

    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::UnsubscriptionFailed,
            "Unsubscribe failed: " + std::string(e.what()));
    }
}

Core::Result<Core::QuoteData> CtpMarketDataProvider::get_latest_quote(
    const Core::Symbol& symbol) {

    // CTP是推送模式，这里返回错误提示使用回调
    return Core::make_error<Core::QuoteData>(Core::ErrorCode::NotSupported,
        "CTP provider uses push mode, please use quote callback");
}

Core::Result<std::vector<Core::QuoteData>> CtpMarketDataProvider::get_quotes(
    const std::vector<Core::Symbol>& symbols) {

    return Core::make_error<std::vector<Core::QuoteData>>(Core::ErrorCode::NotSupported,
        "CTP provider uses push mode, please use quote callback");
}

std::future<Core::Result<Core::QuoteData>> CtpMarketDataProvider::get_latest_quote_async(
    const Core::Symbol& symbol) {

    std::promise<Core::Result<Core::QuoteData>> promise;
    promise.set_value(Core::make_error<Core::QuoteData>(Core::ErrorCode::NotSupported,
        "CTP provider uses push mode, please use quote callback"));
    return promise.get_future();
}

std::future<Core::Result<std::vector<Core::QuoteData>>> CtpMarketDataProvider::get_quotes_async(
    const std::vector<Core::Symbol>& symbols) {

    std::promise<Core::Result<std::vector<Core::QuoteData>>> promise;
    promise.set_value(Core::make_error<std::vector<Core::QuoteData>>(
        Core::ErrorCode::NotSupported,
        "CTP provider uses push mode, please use quote callback"));
    return promise.get_future();
}

Core::Result<bool> CtpMarketDataProvider::health_check() {
    if (!md_api_) {
        return Core::make_result<bool>(false);
    }

    bool healthy = is_connected() && logged_in_;
    return Core::make_result<bool>(healthy);
}

std::string CtpMarketDataProvider::get_version() const noexcept {
    if (md_api_) {
        return std::string(md_api_->GetApiVersion());
    }
    return "Unknown";
}

const char* CtpMarketDataProvider::get_trading_day() const {
    if (md_api_ && logged_in_) {
        return md_api_->GetTradingDay();
    }
    return nullptr;
}

Core::Result<void> CtpMarketDataProvider::set_heartbeat_timeout(std::uint32_t timeout) {
    if (!md_api_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "MD API not initialized");
    }

    md_api_->SetHeartbeatTimeout(timeout);
    return Core::make_success();
}

Core::Result<void> CtpMarketDataProvider::set_udp_buffer_size(std::uint32_t size) {
    if (!md_api_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "MD API not initialized");
    }

    md_api_->SetUDPBufferSize(size);
    return Core::make_success();
}

} // namespace DataHub::Providers
