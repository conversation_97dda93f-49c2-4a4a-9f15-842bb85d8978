{"server": {"host": "0.0.0.0", "port": 8080, "worker_threads": 4, "max_request_size": 1048576, "request_timeout_seconds": 30, "api_prefix": "/api/v1"}, "services": {"datahub": {"enabled": true, "config_file": "config/datahub.json", "auto_start": true, "health_check_interval_seconds": 30}, "trading": {"enabled": true, "config_file": "config/trading.json", "auto_start": true, "health_check_interval_seconds": 30}}, "datahub": {"database": {"type": "sqlite", "connection_string": "data/datahub.db", "pool_size": 10, "timeout_seconds": 30}, "cache": {"enabled": true, "max_size_mb": 256, "ttl_seconds": 300}, "data_sources": {"enabled_sources": ["file", "memory"], "file_data_path": "data/market_data", "real_time_enabled": false}, "quote_service": {"max_symbols": 1000, "update_interval_ms": 1000, "batch_size": 100}, "history_service": {"max_bars_per_request": 10000, "compression_enabled": true, "parallel_requests": 4}}, "trading": {"server": {"port": 8081, "network_threads": 4, "enable_web_interface": true, "enable_api_server": true}, "data_provider": {"type": "composite", "sources": ["datahub"], "cache_ttl_minutes": 5}, "risk_management": {"enabled": true, "max_position_size": 1000000.0, "max_daily_loss": 50000.0, "max_leverage": 10.0, "position_limit_check": true, "real_time_monitoring": true}, "order_management": {"max_orders_per_second": 100, "order_timeout_seconds": 300, "enable_order_validation": true, "enable_duplicate_check": true}, "strategy_management": {"max_concurrent_strategies": 10, "strategy_timeout_seconds": 30, "enable_backtesting": true, "model_directory": "./models"}, "portfolio_management": {"default_currency": "USD", "enable_multi_currency": true, "position_tracking": true, "performance_calculation": true}}, "logging": {"level": "info", "file": "logs/quantservices.log", "console": true, "max_file_size_mb": 100, "max_files": 10, "pattern": "[%Y-%m-%d %H:%M:%S.%e] [%l] [%n] %v", "async": true}, "health_monitoring": {"enabled": true, "check_interval_seconds": 30, "metrics_interval_seconds": 60, "enable_system_metrics": true, "enable_application_metrics": true, "enable_alerts": false, "thresholds": {"cpu_warning_percent": 80.0, "cpu_critical_percent": 95.0, "memory_warning_percent": 80.0, "memory_critical_percent": 95.0, "disk_warning_percent": 85.0, "disk_critical_percent": 95.0, "response_time_warning_ms": 1000.0, "response_time_critical_ms": 5000.0}}, "api": {"cors": {"enabled": true, "allowed_origins": ["*"], "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allowed_headers": ["Content-Type", "Authorization", "X-Requested-With"], "max_age_seconds": 3600}, "rate_limiting": {"enabled": false, "requests_per_minute": 1000, "burst_size": 100}, "authentication": {"enabled": false, "type": "api_key", "api_key": "", "header_name": "X-API-Key"}, "documentation": {"enabled": true, "title": "QuantServices API", "version": "1.0.0", "description": "Integrated DataHub and Trading Services API"}}, "security": {"enable_https": false, "ssl_cert_file": "", "ssl_key_file": "", "enable_request_validation": true, "max_request_body_size": 1048576}, "performance": {"enable_metrics": true, "metrics_retention_hours": 24, "enable_profiling": false, "thread_pool_size": 8}}